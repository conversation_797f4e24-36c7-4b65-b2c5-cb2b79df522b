from typing import Any
import os
from inspect_ai.model import (
    ChatCompletionChoice,
    ChatMessage,
    ContentText,
    GenerateConfig,
    ModelOutput,
    modelapi,
)
from inspect_ai.model._model_call import ModelCall
from inspect_ai.model._model_output import ModelUsage, as_stop_reason
from inspect_ai.model._providers.openai import OpenAIAPI
from inspect_ai.model._providers.openai_o1 import (
    O1PreviewChatAPIHandler,
    chat_choices_from_response,
    chat_messages,
    handle_bad_request,
)
from inspect_ai.tool import ToolChoice, ToolInfo
from openai import BadRequestError
from openai.types.chat import ChatCompletion, ChatCompletionMessageParam

GEMMA_BASE_URL = str(os.environ.get("GEMMA_BASE_URL", None))


def merge_messages(
    messages: list[ChatCompletionMessageParam],
) -> list[ChatCompletionMessageParam]:
    """
    Merge a list of ChatCompletionMessageParams into a structured format:
    1. A single system message first (if present)
    2. Then alternating user and assistant messages, where consecutive user/tool/function
       messages are merged into a single user message

    Args:
        messages: List of message parameters

    Returns:
        List of merged messages in the desired structure
    """
    if not messages:
        return []

    result = []
    current_content = None
    current_role = None

    for i, msg in enumerate(messages):
        if msg.get("role") == "system":
            result.append(msg)
            messages = messages[:i] + messages[i + 1 :]
            break

    for msg in messages:
        role = msg.get("role")

        if role == "developer":
            continue

        if current_role is None:
            # First message
            if role in ["user", "tool", "function"]:
                current_role = "user"
                current_content = _extract_content(msg)
            else:
                result.append(msg)

        elif role in ["user", "tool", "function"] and current_role == "user":
            # Merge consecutive user/tool/function messages
            current_content += f"\n\n{_extract_content(msg)}"

        elif role == "assistant" and current_role == "user":
            # Finalize the current user message and start assistant message
            result.append({"role": "user", "content": current_content})
            result.append(msg)
            current_role = "assistant"
            current_content = None

        elif role in ["user", "tool", "function"] and current_role == "assistant":
            # Start a new user message after assistant
            current_role = "user"
            current_content = _extract_content(msg)

        else:
            # Other cases, just add the message
            result.append(msg)
            current_role = role
            current_content = None

    # Add any pending message
    if current_role == "user" and current_content is not None:
        result.append({"role": "user", "content": current_content})

    return result


def _extract_content(msg: ChatCompletionMessageParam) -> str:
    """
    Extract content from different message types with appropriate formatting.
    """
    role = msg.get("role", "")
    content = msg.get("content", "")

    if role == "function":
        function_name = msg.get("name", "function")
        return f"Function {function_name} returned: {content}"
    elif role == "tool":
        tool_name = msg.get("name", "tool")
        return f"Tool {tool_name} returned: {content}"
    else:
        return content if content else ""


@modelapi(name="gemma")
class GemmaProvider(OpenAIAPI):
    def __init__(
        self,
        model_name: str,
        base_url: str | None = None,
        api_key: str | None = None,
        config: GenerateConfig = GenerateConfig(),
        **model_args: Any,
    ) -> None:
        super().__init__(
            model_name=model_name,
            base_url=GEMMA_BASE_URL,
            config=config,
            responses_api=False,
            **model_args,
        )

    def is_o_series(self):
        return True

    def is_o1_mini(self) -> bool:
        return False

    def is_o1_early(self) -> bool:
        return True

    def is_gpt(self) -> bool:
        return False

    async def generate(
        self,
        input: list[ChatMessage],
        tools: list[ToolInfo],
        tool_choice: ToolChoice,
        config: GenerateConfig,
    ) -> ModelOutput | tuple[ModelOutput | Exception, ModelCall]:
        params = self.completion_params(config, False)

        model = params["model"]
        handler = O1PreviewChatAPIHandler(model=model)

        output_chat_messages = chat_messages(input, tools, handler)
        request = dict(
            messages=merge_messages(output_chat_messages),
            **params,
        )
        response: dict[str, Any] = {}

        def model_call() -> ModelCall:
            return ModelCall.create(
                request=request,
                response=response,
            )

        try:
            completion: ChatCompletion = await self.client.chat.completions.create(
                **request
            )
            response = completion.model_dump()
        except BadRequestError as ex:
            return handle_bad_request(model, ex), model_call()

        try:
            choices = chat_choices_from_response(completion, tools, handler)

            return ModelOutput(
                model=completion.model,
                choices=choices,
                usage=ModelUsage(
                    input_tokens=completion.usage.prompt_tokens,
                    output_tokens=completion.usage.completion_tokens,
                    input_tokens_cache_read=None,
                    reasoning_tokens=None,
                    total_tokens=completion.usage.total_tokens,
                )
                if completion.usage
                else None,
            ), model_call()
        except Exception as e:
            print(f"Error processing response: {e}")
            # Fallback to a simpler response format
            from inspect_ai.model import ChatMessageAssistant

            choices = [
                ChatCompletionChoice(
                    message=ChatMessageAssistant(
                        role="assistant",
                        content=ContentText(text=choice.message.content),
                        id=choice.message.id if hasattr(choice.message, "id") else None,
                        source="generate",
                        model=model,
                    ),
                    stop_reason=as_stop_reason(choice.finish_reason),
                    logprobs=None,
                )
                for choice in completion.choices
            ]

            return ModelOutput(
                model=completion.model,
                choices=choices,
                usage=ModelUsage(
                    input_tokens=completion.usage.prompt_tokens,
                    output_tokens=completion.usage.completion_tokens,
                    input_tokens_cache_read=None,
                    reasoning_tokens=None,
                    total_tokens=completion.usage.total_tokens,
                )
                if completion.usage
                else None,
            ), model_call()
