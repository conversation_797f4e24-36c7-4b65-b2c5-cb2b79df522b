import asyncio
import pytest
from inspect_ai.model import ChatMessageUser, GenerateConfig
from gemma_tools.gemma_provider import <PERSON><PERSON><PERSON><PERSON>

def create_chat_message(content):
    return ChatMessageUser(
        content=content)

@pytest.mark.asyncio 
async def test_gemma():

    provider = Gemma<PERSON>rovider(
        model_name="gemma-3-27b-it",
        config=GenerateConfig()  
    )
    
    messages = [
        create_chat_message(content="What's the capital of France?")
    ]
    
    output, model_call = await provider.generate(
        input=messages,
        tools=[],
        tool_choice=None,
        config=GenerateConfig(max_tokens=600)  
    )

    print("\nModel:", output.model)
    print("\nResponse:")
    response_text = ""
    for choice in output.choices:
        if hasattr(choice.message, "content") and hasattr(choice.message.content, "text"):
            response_text = choice.message.content.text
            print(response_text)
        else:
            response_text = str(choice.message.content)
            print(response_text)
        print("---> End of response. <PERSON> likes to leave some trailing whitespace.")
    
    assert "Paris" in response_text or "paris" in response_text.lower(), f"Expected 'Paris' in response, but got: {response_text}"

    if output.usage:
        print("\nToken Usage:")
        print(f"Input tokens: {output.usage.input_tokens}")
        print(f"Output tokens: {output.usage.output_tokens}")
        print(f"Total tokens: {output.usage.total_tokens}")

if __name__ == "__main__":
    asyncio.run(test_gemma())