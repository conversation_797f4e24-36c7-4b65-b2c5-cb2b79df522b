[tool.poetry]
name = "naisi-cyber-testing"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<PERSON>@dsit.gov.uk>", "Ekin Zorer"]

packages = [
    { include = "gemma_tools", from = "src" },
    { include = "agents", from = "src" },
    { include = "azure_tools", from = "src" }
]

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
inspect-ai = "0.3.99"
inspect-evals = { git = "https://github.com/UKGovernmentBEIS/inspect_evals" }
vllm = "^0.8.5.post1"
click = "^8.1.7"
azure-ai-inference = "^1.0.0b9"
ucb = { git = "https://github.com/MSchmatzAISI/aisi-cyber-evals.git", branch = "fix-build-deps" }
anyio = "^4.9.0"

[tool.poetry.plugins.inspect_ai]
gemma_tools = "gemma_tools.gemma_provider"
azure_tools = "azure_tools.azure_provider"
intercode_agent = "agents.intercode_agent"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


