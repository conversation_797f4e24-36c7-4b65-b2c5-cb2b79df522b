FROM ubuntu:latest

RUN apt-get update && \
    apt-get install -y bash python3 python3-pip python3-venv psmisc bsdmainutils cron imagemagick tcpdump dnsutils git wget gcc sleuthkit netcat-traditional unzip curl gdb poppler-utils tmux jq vim nano && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Alias python to python3
RUN ln -s /usr/bin/python3 /usr/bin/python

ENV PIP_BREAK_SYSTEM_PACKAGES 1
RUN pip3 install pyshark scapy pwntools dpkt pycryptodome cryptography owiener numpy sympy requests Pillow python-whois PyPDF2 flake8

# Add inspect-tool-support with specific version requirement
ENV PATH="$PATH:/opt/inspect_tool_support/bin"
RUN python3 -m venv /opt/inspect_tool_support && \
    /opt/inspect_tool_support/bin/pip install 'inspect-tool-support>=1.0.0' && \
    /opt/inspect_tool_support/bin/inspect-tool-support post-install

# Create the UCB virtual environment at /opt/ucb
RUN python3 -m venv /opt/ucb

# Install cybersecurity tools in the UCB virtual environment
RUN /opt/ucb/bin/pip install --upgrade pip && \
    /opt/ucb/bin/pip install \
    cryptography \
    pwntools \
    scapy \
    pyshark \
    r2pipe \
    angr \
    pycryptodome \
    requests \
    beautifulsoup4 \
    ghidra-bridge \
    Pillow \
    python-whois \
    PyPDF2 \
    sympy \
    numpy

RUN apt-get update && \
    apt-get install -y radare2 binwalk file xxd hexedit && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Add a script to activate the UCB environment automatically when bash is run
RUN echo 'if [ -f /opt/ucb/bin/activate ]; then source /opt/ucb/bin/activate; fi' >> /root/.bashrc

# Ensure the UCB bin directory is in the PATH
ENV PATH="/opt/ucb/bin:${PATH}"

WORKDIR /tmp