import pandas as pd
from inspect_ai.log import <PERSON><PERSON><PERSON><PERSON>
from inspect_ai.log import list_eval_logs, <PERSON>lLog, read_eval_log
from pathlib import Path

import pprint
pp = pprint.PrettyPrinter(indent=4).pprint

def eval_logs_to_sample_df(eval_logs : EvalLog | list[EvalLog]):
    """Get a list of all the samples from a list of output logs."""

    if not isinstance(eval_logs, list):
        eval_logs = [eval_logs]
     
    output_rows =  []
    for eval_log in eval_logs:
        model_name = eval_log.eval.model.split("/")[1]
        dataset_name = eval_log.eval.dataset.name
        task_name = eval_log.eval.task
        
        samples = eval_log.samples
        if samples is not None:
            for sample in samples:
                
                sample_id = sample.id
                epoch=sample.epoch
                sample_target = sample.target   
                sample_score = sample.scores['includes'].value if sample.scores else "Error"
                final_submission = sample.scores['includes'].answer if sample.scores else "Error"
                output_rows += [{"id": sample_id, "epoch": epoch, "score" : sample_score, "target": sample_target, "final_submission": final_submission, "model": model_name, "dataset": dataset_name, "task": task_name}]
    
    return pd.DataFrame(output_rows)

def eval_logs_to_sample_metadata(eval_logs : EvalLog | list[EvalLog]):
    """Get a list of all the samples from a list of output logs."""

    if not isinstance(eval_logs, list):
        eval_logs = [eval_logs]
     
    output_rows =  []
    for eval_log in eval_logs:
        model_name = eval_log.eval.model.split("/")[1]
        dataset_name = eval_log.eval.dataset.name
        task_name = eval_log.eval.task
        
        samples = eval_log.samples
        if samples is not None:
            for sample in samples:
                
                sample_data = sample.transcript
                print(sample_data)
                break
    
    

log_file_name = "inspect_ai/data/Intercode/left/2025-06-20T09-56-26+10-00_gdm-intercode-ctf_no_bash_session_tool.eval"
eval_log = read_eval_log(log_file_name)
eval_logs_to_sample_metadata(eval_log)

import glob
def inspect_sample_structure(eval_file_path):
    """
    Inspect the structure of samples in an eval file and print column information.
    
    Args:
        eval_file_path: Path to the .eval file
    """
    # Read the eval log
    eval_log = read_eval_log(eval_file_path)
    
    # Get the first sample for inspection
    if eval_log.samples and len(eval_log.samples) > 0:
        sample = eval_log.samples[0]
        
        print("Sample Structure:")
        print(f"Sample type: {type(sample)}")
        print("\nSample attributes:")
        for attr in dir(sample):
            if not attr.startswith('_'):  # Skip private attributes
                try:
                    value = getattr(sample, attr)
                    print(f"  {attr}: {type(value)}")
                    
                    # For scores, show more details
                    if attr == 'scores' and value:
                        print("    Score keys:")
                        for score_key in value:
                            print(f"      {score_key}: {type(value[score_key])}")
                            if hasattr(value[score_key], 'value'):
                                print(f"        value: {value[score_key].value}")
                            if hasattr(value[score_key], 'answer'):
                                print(f"        answer: {value[score_key].answer}")
                except Exception as e:
                    print(f"  {attr}: Error accessing - {str(e)}")
        
        # Convert sample to DataFrame row and show columns
        sample_df = eval_logs_to_sample_df(eval_log)
        print("\nDataFrame columns:")
        for col in sample_df.columns:
            print(f"  {col}")
        
        # Show first row as example
        print("\nExample row:")
        print(sample_df.iloc[0])
    else:
        print("No samples found in the eval log")

# Test with the first eval file
eval_files = glob.glob(os.path.join("inspect_ai/data/", "**/*.eval"), recursive=True)
if eval_files:
    inspect_sample_structure(eval_files[0])
else:
    print("No .eval files found")

def summarize_and_save_results(log_file_name, df, output_path="results_summary.csv"):
    """
    Summarize evaluation results with C/I scoring, group by epoch, and save to CSV.
    
    Args:
        log_file_name: Name of the log file being processed
        df: DataFrame containing evaluation results
        output_path: Path to save the CSV file, or None to skip saving
    """
    # Convert score to numeric (1/0) for calculation
    df['score_numeric'] = df['score'].map({'C': 1, 'I': 0})
    
    # Group by epoch and calculate metrics
    if 'epoch' in df.columns:
        epoch_summary = df.groupby('epoch').agg(
            accuracy=('score_numeric', 'mean'),
            total_samples=('score_numeric', 'count'),
            correct_count=('score_numeric', 'sum')
        ).reset_index()
        
        # Calculate overall metrics
        overall = pd.DataFrame({
            'log_file_name': [log_file_name],
            'epoch': ['overall'],
            'accuracy': [df['score_numeric'].mean()],
            'total_samples': [len(df)],
            'correct_count': [df['score_numeric'].sum()]
        })
        
        # Combine overall and epoch summaries
        summary = pd.concat([overall, epoch_summary], ignore_index=True)
        
        # Save to CSV if output_path is provided
        if output_path:
            summary.to_csv(output_path, index=False)
            print(f"Results saved to {output_path}")
        
        return summary
    else:
        print("No 'epoch' column found in the dataframe")
        return None
import os
import pandas as pd
import json
import glob

def process_all_eval_files(directory=".", output_path="combined_results_summary.csv"):
    """
    Process all .eval files in the directory, summarize their results, and save to a single CSV file.
    
    Args:
        directory: Directory to search for .eval files
        output_path: Path to save the combined CSV file
    """
    # Find all .eval files
    eval_files = glob.glob(os.path.join(directory, "**/*.eval"), recursive=True)
    print(f"Found {len(eval_files)} .eval files")
    
    all_summaries = []
    
    for eval_file in eval_files:
        try:
            # Extract file name for reference
            file_name = os.path.basename(eval_file)
            
            # Extract subfolder name
            rel_path = os.path.relpath(os.path.dirname(eval_file), directory)
            subfolder = rel_path if rel_path != "." else ""
            
            # Remove timestamp from file name if present
            # Pattern: YYYY-MM-DDThh-mm-ss+hh-mm_rest-of-name
            clean_name = file_name
            if "_" in file_name:
                timestamp_part = file_name.split("_")[0]
                if "T" in timestamp_part and "+" in timestamp_part:
                    # Remove timestamp part
                    clean_name = file_name[file_name.find("_")+1:]
            
            print(f"Processing {file_name}... (Clean name: {clean_name}, Subfolder: {subfolder})")
            
            # Use inspect_ai's read_eval_log function
            eval_log = read_eval_log(eval_file)
            
            # Convert samples to DataFrame
            sample_df = eval_logs_to_sample_df(eval_log)
            
            # Summarize this file's results (don't save individual files)
            summary = summarize_and_save_results(clean_name, sample_df, output_path=None)
            
            if summary is not None:
                # Add file name and subfolder to each row
                summary['log_file_name'] = clean_name
                if subfolder:
                    summary['subfolder'] = subfolder
                
                all_summaries.append(summary)
            
        except Exception as e:
            print(f"Error processing {eval_file}: {str(e)}")
    
    if all_summaries:
        # Combine all summaries
        combined_summary = pd.concat(all_summaries, ignore_index=True)
        
        # Save to CSV
        combined_summary.to_csv(output_path, index=False)
        print(f"Combined results saved to {output_path}")
        
        return combined_summary
    else:
        print("No valid summaries generated")
        return None

def summarize_and_save_results(log_file_name, df, output_path="results_summary.csv"):
    """
    Summarize evaluation results with C/I scoring, group by epoch, and save to CSV.
    
    Args:
        log_file_name: Name of the log file being processed
        df: DataFrame containing evaluation results
        output_path: Path to save the CSV file, or None to skip saving
    """
    # Convert score to numeric (1/0) for calculation
    df['score_numeric'] = df['score'].map({'C': 1, 'I': 0})
    
    # Group by epoch and calculate metrics
    if 'epoch' in df.columns:
        epoch_summary = df.groupby('epoch').agg(
            accuracy=('score_numeric', 'mean'),
            total_samples=('score_numeric', 'count'),
            correct_count=('score_numeric', 'sum')
        ).reset_index()
        
        # Calculate overall metrics
        overall = pd.DataFrame({
            'epoch': ['overall'],
            'accuracy': [df['score_numeric'].mean()],
            'total_samples': [len(df)],
            'correct_count': [df['score_numeric'].sum()]
        })
        
        # Combine overall and epoch summaries
        summary = pd.concat([overall, epoch_summary], ignore_index=True)
        
        # Save to CSV if output_path is provided
        if output_path:
            summary.to_csv(output_path, index=False)
            print(f"Results saved to {output_path}")
        
        return summary
    else:
        print("No 'epoch' column found in the dataframe")
        return None

# Run the function to process all eval files
combined_results = process_all_eval_files(directory="inspect_ai/data/", output_path="inspect_ai/data/combined_eval_results.csv")
display(combined_results)


#Single Analysis
import os 
print("Current directory:", os.getcwd())
log_file_name = "inspect_ai/data/Intercode/2025-06-24T07-19-17+10-00_cybench_one_shot_coa_reasoning_final.eval"
eval_log = read_eval_log(log_file_name)
sample_df = eval_logs_to_sample_df(eval_log)
print(sample_df.columns)
# Apply to your sample dataframe and save results
summary = summarize_and_save_results(log_file_name,sample_df, "one_shot_coa_reasoning_intercode.csv")
display(summary)



import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import os

# === CONFIGURATION ===
sns.set(style="whitegrid", font_scale=1.2)
plt.rcParams["figure.dpi"] = 150
plt.rcParams["savefig.bbox"] = 'tight'
output_dir = "/Users/<USER>/Desktop/Projects/AISI/"

# === LOAD DATA ===
file_path = output_dir+"results.csv"  # Update path if needed
df = pd.read_csv(file_path)
df["Pass @ K"] = df["Pass @ K"].astype(str)

overall_df = df[df["Pass @ K"] == "overall"]
passk_df = df[df["Pass @ K"] != "overall"].copy()
passk_df["Pass @ K"] = passk_df["Pass @ K"].astype(int)

# === UTILITY ===
def save_plot(name):
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{name}.png"))
    plt.close()

# === OVERALL Accuracy PLOTS ===

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import os

# === CONFIGURATION ===
sns.set(style="whitegrid", font_scale=1.2)
plt.rcParams["figure.dpi"] = 150
plt.rcParams["savefig.bbox"] = 'tight'
output_dir = "/Users/<USER>/Desktop/Projects/AISI/"

# === LOAD DATA ===
file_path = output_dir + "results.csv"
df = pd.read_csv(file_path)
df["Pass @ K"] = df["Pass @ K"].astype(str)

# Separate overall from pass@k, but ignore 'overall' going forward
passk_df = df[df["Pass @ K"] != "overall"].copy()
passk_df["Pass @ K"] = passk_df["Pass @ K"].astype(int)

# Optional: if needed in other parts
overall_df = df[df["Pass @ K"] == "overall"]

# === UTILITY ===
def save_plot(name):
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{name}.png"))
    plt.close()

# === BASELINE COMPARISON (Median & Std Dev) ===
def plot_baseline_comparison():
    data = passk_df[(passk_df["Variation"] == "baseline") & (passk_df["Temp"] == 1.0)]

    if not data.empty:
        grouped = data.groupby("Benchmark")["Accuracy"].agg(['mean', 'std', 'median', 'min', 'max']).reset_index()
        grouped.rename(columns={"mean": "Accuracy", "std": "stddev"}, inplace=True)

        # Print nicely for slide/table
        print("| Benchmark     | Accuracy (Mean ± Std. Dev) | Median  | Min     | Max     |")
        print("|---------------|-----------------------------|---------|---------|---------|")
        for _, row in grouped.iterrows():
            print(f"| {row['Benchmark']:<13} | {row['Accuracy']:.4f} ± {row['stddev']:.4f}         | "
                  f"{row['median']:.4f}  | {row['min']:.4f}  | {row['max']:.4f}  |")

        # Bar plot using Mean ± Std Dev
        plt.figure(figsize=(5, 4))
        plt.bar(
            grouped["Benchmark"],
            grouped["Accuracy"],
            yerr=grouped["stddev"],
            capsize=5,
            color=sns.color_palette("Set2", n_colors=len(grouped)),
            edgecolor='black'
        )

        plt.title("Baseline Accuracy ± Std. Dev. (Temp = 1.0)", fontsize=13)
        plt.xlabel("Benchmark", fontsize=11, fontweight="bold")
        plt.ylabel("Accuracy", fontsize=11, fontweight="bold")
        plt.ylim(0, 1)
       
        plt.grid(axis='y', linestyle='--', alpha=0.4)
        plt.tight_layout()
        save_plot("baseline_comparison_temp1_stddev")

# === PLACEHOLDER: Keep Other Plots As Needed ===

def plot_passk_boxplot():
    if not passk_df.empty:
        plt.figure(figsize=(10, 6))
        sns.boxplot(data=passk_df, x="Pass @ K", y="Accuracy", hue="Benchmark", palette="pastel")
        plt.title("Pass@K Accuracy Distribution Across Tools")
        save_plot("passk_boxplot_distribution")

# === EXECUTION ===





# 2. Variations 1–4: Cybench vs Intercode (bar plots side-by-side for each tool)
def plot_variations_1_4():
    variations = ["baseline", "var1", "var2", "var3", "var4"]
    data = overall_df[(overall_df["Variation"].isin(variations)) & (overall_df["Temp"] == 1.0)]

    if not data.empty:
        fig, axes = plt.subplots(1, 2, figsize=(12, 5), sharey=True)

        for idx, tool in enumerate(["Cybench", "Intercode"]):
            subset = data[data["Benchmark"] == tool]
            if not subset.empty:
                # Ensure consistent order and colors
                subset["Variation"] = pd.Categorical(subset["Variation"], categories=variations, ordered=True)
                sns.barplot(data=subset, x="Variation", y="Accuracy", palette="Set2", ax=axes[idx])
                axes[idx].set_title(f"{tool}")
                axes[idx].set_ylabel("Accuracy")
                axes[idx].set_xlabel("Variation")

        fig.suptitle("Overall Accuracy by Variation (1–4) vs Baseline\nTemp = 1.0", fontsize=14)
        plt.tight_layout()
        save_plot("variation_1_4_separated_by_tool")

def plot_tool_variations():
    data = overall_df[(overall_df["Variation"].isin(["baseline", "var5", "var6"])) & (overall_df["Temp"] == 1.0)]
    if not data.empty:
        plt.figure(figsize=(8, 5))
        sns.barplot(data=data, x="Variation", y="Accuracy", hue="Benchmark", palette="Set1")
        plt.title("Tool Variations (var5–var6) vs Baseline (Temp = 1.0)")
        plt.ylabel("Accuracy")
        save_plot("tool_variations_5_6")


def plot_temperature_effects_grouped():
    # Use actual Pass@K values (not "overall") for baseline and var7 (regardless of Temp)
    data = passk_df[passk_df["Variation"].isin(["baseline", "var7"])]

    if data.empty:
        print("No data available for 'baseline' and 'var7'.")
        return

    # Compute mean and std by Benchmark and Variation
    grouped = data.groupby(["Benchmark", "Variation"])["Accuracy"].agg(['mean', 'std']).reset_index()
    grouped.rename(columns={'mean': 'Accuracy', 'std': 'StdDev'}, inplace=True)

    # Ensure both variations are aligned across benchmarks
    benchmarks = sorted(grouped["Benchmark"].unique())
    variations = ["baseline", "var7"]
    x = np.arange(len(benchmarks))  # Bar positions

    width = 0.35
    fig, ax = plt.subplots(figsize=(8, 6))

    for i, variation in enumerate(variations):
        # Align data for all benchmarks
        var_data = grouped[grouped["Variation"] == variation].set_index("Benchmark").reindex(benchmarks)
        means = var_data["Accuracy"].values
        stds = var_data["StdDev"].values

        ax.bar(
            x + i * width,
            means,
            width,
            yerr=stds,
            capsize=5,
            label=variation,
            color=sns.color_palette("Set2")[i],
            edgecolor="black"
        )

    ax.set_xticks(x + width / 2)
    ax.set_xticklabels(benchmarks)
    ax.set_ylabel("Accuracy")
    ax.set_ylim(0, 1)
    ax.set_yticks(np.arange(0, 1.0, 0.1))
    ax.set_title("Accuracy: Baseline (T=1.0) vs Var7 (T=0.7)\nGrouped by Benchmark")
    ax.grid(axis='y', linestyle='--', alpha=0.4)
    ax.legend(title="Variation")
    plt.tight_layout()
    save_plot("temperature_effects_baseline_vs_var7_grouped")
    plt.close()






# 2. Variations 1–4: Cybench vs Intercode (bar plots side-by-side for each tool)
def plot_variations_1_4():
    variations = ["baseline", "var1", "var2", "var3", "var4"]
    data = overall_df[(overall_df["Variation"].isin(variations)) & (overall_df["Temp"] == 1.0)]

    if not data.empty:
        fig, axes = plt.subplots(1, 2, figsize=(12, 5), sharey=True)

        for idx, tool in enumerate(["Cybench", "Intercode"]):
            subset = data[data["Benchmark"] == tool]
            if not subset.empty:
                # Ensure consistent order and colors
                subset["Variation"] = pd.Categorical(subset["Variation"], categories=variations, ordered=True)
                sns.barplot(data=subset, x="Variation", y="Accuracy", palette="Set2", ax=axes[idx])
                axes[idx].set_title(f"{tool}")
                axes[idx].set_ylabel("Accuracy")
                axes[idx].set_xlabel("Variation")

        fig.suptitle("Overall Accuracy by Variation (1–4) vs Baseline\nTemp = 1.0", fontsize=14)
        plt.tight_layout()
        save_plot("variation_1_4_separated_by_tool")


# === PASS@K ANALYSIS ===

def plot_passk_lines_by_tool():
    data = passk_df[(passk_df["Variation"] == "baseline") & (passk_df["Temp"] == 1.0)]
    if not data.empty:
        plt.figure(figsize=(10, 6))
        sns.lineplot(data=data, x="Pass @ K", y="Accuracy", hue="Benchmark", marker="o")
        plt.title("Pass@K Accuracy - Baseline Comparison (Temp = 1.0)")
        save_plot("passk_baseline_comparison")

def plot_passk_variations_1_4():
    data = passk_df[(passk_df["Variation"].isin(["baseline", "var1", "var2", "var3", "var4"])) &
                    (passk_df["Temp"] == 1.0)]
    if not data.empty:
        plt.figure(figsize=(12, 6))
        sns.lineplot(data=data, x="Pass @ K", y="Accuracy", hue="Variation", style="Benchmark", marker="o")
        plt.title("Pass@K Accuracy: Baseline vs Variations 1–4 (Temp = 1.0)")
        save_plot("passk_variations_1_4")

def plot_passk_heatmap():
    benchmarks = passk_df["Benchmark"].unique()
    if len(benchmarks) == 0:
        print("No data available in passk_df.")
        return

    fig, axes = plt.subplots(1, len(benchmarks), figsize=(14, 6), sharey=True)

    if len(benchmarks) == 1:
        axes = [axes]  # Ensure it's iterable

    for ax, benchmark in zip(axes, benchmarks):
        subset = passk_df[passk_df["Benchmark"] == benchmark]
        heat_data = subset.groupby(["Variation", "Pass @ K"])["Accuracy"].mean().unstack()

        sns.heatmap(
            heat_data,
            annot=True,
            cmap="coolwarm",
            fmt=".2f",
            ax=ax,
            cbar=True if ax == axes[-1] else False
        )

        ax.set_title(f"{benchmark}")
        ax.set_xlabel("Pass @ K")
        ax.set_ylabel("Variation")

    fig.suptitle("Accuracy Heatmap by Variation and Pass@K per Benchmark", fontsize=14)
    plt.tight_layout()
    save_plot("passk_accuracy_heatmap_by_benchmark")
    plt.close()


# === EXECUTION ===

plot_baseline_comparison()
plot_variations_1_4()
plot_tool_variations()
plot_temperature_effects_grouped()


plot_passk_lines_by_tool()
plot_passk_variations_1_4()
plot_passk_boxplot()
plot_passk_heatmap()



def plot_median_accuracy_variations_1_4():
    variations = ["baseline", "var1", "var2", "var3", "var4"]
    
    # Only take Pass@K 1–10 (exclude 'overall')
    data = passk_df[
        (passk_df["Variation"].isin(variations)) &
        (passk_df["Temp"] == 1.0) &
        (passk_df["Pass @ K"].astype(str).str.isdigit())
    ].copy()

    if data.empty:
        print("No valid Pass@K data (1–10) found.")
        return

    # Convert Pass@K to integer for sorting if needed
    data["Pass @ K"] = data["Pass @ K"].astype(int)

    # Compute median accuracy per (Variation, Benchmark)
    median_df = (
        data.groupby(["Variation", "Benchmark"])["Accuracy"]
        .median()
        .reset_index()
        .rename(columns={"Accuracy": "Median Accuracy"})
    )

    # Print the computed medians
    print("\n📊 Median Accuracy (from Pass@K 1–10):")
    print(median_df.to_string(index=False))

    # Plot
    plt.figure(figsize=(10, 6))
    sns.barplot(
        data=median_df,
        x="Variation",
        y="Median Accuracy",
        hue="Benchmark",
        palette="Set2"
    )

    plt.title("Median Accuracy Across Pass@K (1–10)\nVariations 1–4 vs Baseline (Temp = 1.0)")
    plt.ylabel("Median Accuracy")
    plt.xlabel("Variation")
    plt.ylim(0, 1)
    plt.legend(title="Benchmark")
    plt.tight_layout()
    save_plot("variation_1_4_median_accuracy_barplot")
    plt.close()

plot_median_accuracy_variations_1_4()

import matplotlib.pyplot as plt
import numpy as np

import matplotlib.pyplot as plt
import numpy as np

def plot_variations_boxplot(save_path):
    variations = sorted(df["Variation"].unique())
    benchmarks = df["Benchmark"].unique()
    colors = {"Cybench": "#1f77b4", "Intercode": "#ff7f0e"}  # blue & orange

    fig, ax = plt.subplots(figsize=(8, 5))
    box_width = 0.3
    x = np.arange(len(variations))

    for i, benchmark in enumerate(benchmarks):
        positions = x - box_width/2 + i*box_width
        data_to_plot = [df[(df["Variation"] == v) & (df["Benchmark"] == benchmark)]["Accuracy"].values for v in variations]

        bp = ax.boxplot(
            data_to_plot,
            positions=positions,
            widths=box_width,
            patch_artist=True,
            showmeans=False,          # Disable mean marker
            showfliers=False,         # No outliers
            medianprops=dict(color='black', linestyle='dotted', linewidth=1.5),
            boxprops=dict(linewidth=1.2),
            whiskerprops=dict(linewidth=1.2),
            capprops=dict(linewidth=1.2)
        )

        # Set box face color and transparency
        for patch in bp['boxes']:
            patch.set_facecolor(colors[benchmark])
            patch.set_alpha(0.7)

        # Add mean lines manually as solid red lines inside each box
        for pos, d in zip(positions, data_to_plot):
            if len(d) > 0:
                mean_val = np.mean(d)
                ax.hlines(mean_val, pos - box_width/2 + 0.05, pos + box_width/2 - 0.05, colors='red', linewidth=2)

    # Axis labels and ticks
    ax.set_xticks(x)
    ax.set_xticklabels(variations, fontsize=10)
    ax.set_xlabel("Variation", fontsize=11, fontweight='bold')
    ax.set_ylabel("Accuracy", fontsize=11, fontweight='bold')
    ax.set_title("Accuracy Distribution by Variation and Benchmark", fontsize=12, fontweight='bold', pad=12)
    ax.set_ylim(0, 1)
    ax.yaxis.set_ticks(np.arange(0, 1.01, 0.1))  # tighter granularity 0.1 increments
    ax.tick_params(axis='both', which='major', labelsize=9)
    ax.grid(axis='y', linestyle='--', linewidth=0.7, alpha=0.5)
    ax.set_facecolor('white')
    fig.patch.set_facecolor('white')

    # Legend for benchmarks outside upper right
    handles = [plt.Line2D([0], [0], color=colors[b], lw=8, alpha=0.7) for b in benchmarks]
    ax.legend(handles, benchmarks, title="Benchmark", fontsize=11, frameon=False,
              loc='upper left', bbox_to_anchor=(1.02, 1))

    plt.tight_layout(pad=1.0, rect=[0, 0, 0.85, 1])  # leave space on right for legend
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close(fig)  # Close to free memory
    print(f"Plot saved to {save_path}")


# Usage example
plot_variations_boxplot(save_path="/Users/<USER>/Desktop/Projects/AISI/boxplot_variations.png")


def print_variation_benchmark_summary():
    summary_rows = []
    variations = sorted(df["Variation"].unique())
    benchmarks = df["Benchmark"].unique()

    for v in variations:
        for b in benchmarks:
            subset = df[(df["Variation"] == v) & (df["Benchmark"] == b)]["Accuracy"]
            if subset.empty:
                continue
            median = subset.median()
            minimum = subset.min()
            maximum = subset.max()
            mean = subset.mean()
            summary_rows.append([v, b, median, minimum, maximum, mean])
    
    summary_df = pd.DataFrame(summary_rows, columns=["Variation", "Benchmark", "Median", "Min", "Max", "Mean"])
    print(summary_df.to_string(index=False))

print_variation_benchmark_summary()


import pandas as pd
import matplotlib.pyplot as plt

# Filter accuracy values by benchmark
cybench_accuracies = df[df['Benchmark'] == 'Cybench']['Accuracy']
intercode_accuracies = df[df['Benchmark'] == 'Intercode']['Accuracy']

plt.figure(figsize=(6, 6))

box = plt.boxplot(
    [cybench_accuracies, intercode_accuracies],
    labels=["Cybench", "Intercode"],
    showmeans=False,
    boxprops=dict(linewidth=1.2),
    whiskerprops=dict(linewidth=1.2),
    capprops=dict(linewidth=1.2),
    flierprops=dict(marker='o', markersize=3, linestyle='none', markerfacecolor='gray', alpha=0.5)
)

means = [cybench_accuracies.mean(), intercode_accuracies.mean()]
medians = [cybench_accuracies.median(), intercode_accuracies.median()]

positions = [1, 2]

# Get the boxes (matplotlib Line2D objects)
for pos, mean, median, box_line in zip(positions, means, medians, box['boxes']):
    # box_line is a Line2D object representing the box; get its x data
    xdata = box_line.get_xdata()
    left, right = xdata[0], xdata[2]  # left and right x-coords of the box

    # Draw mean line (red solid) inside the box width
    plt.hlines(mean, left, right, colors='red', linestyles='-', linewidth=0.75)

    # Draw median line (black dotted) inside the box width
    plt.hlines(median, left, right, colors='black', linestyles=':', linewidth=0.75)

plt.ylabel("Accuracy", fontsize=12)
plt.title("Overall Accuracy Distribution: Cybench vs Intercode", fontsize=13, fontweight='bold')
plt.ylim(0, 1)
plt.yticks([i/10 for i in range(1, 10)])
plt.grid(axis='y', linestyle='--', alpha=0.5)
plt.tight_layout()

plt.savefig("/Users/<USER>/Desktop/Projects/AISI/boxplot_cybench_intercode.png", dpi=300)
plt.show()
