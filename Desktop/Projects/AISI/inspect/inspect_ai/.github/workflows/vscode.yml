on:
  push:
    tags:
      - "v[0-9]*"
    paths:
      - "tools/vscode/**"
      - ".github/workflows/vscode.yml"
    branches:
      - "main"
  pull_request:
    branches:
      - "main"
    paths:
      - "tools/vscode/**"
      - ".github/workflows/vscode.yml"
  workflow_dispatch:

name: Build VS Code Ext
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "18.x"
      - run: |
          pushd tools/vscode
          yarn install --immutable --immutable-cache --check-cache

      - name: Lint
        run: |
          pushd tools/vscode
          yarn lint

      - name: Formatting
        run: |
          pushd tools/vscode
          yarn prettier --check .

      - name: Build Extension
        run: |
          pushd tools/vscode
          yarn vsce package

      - name: Setup Xvfb
        run: |
          sudo apt-get update
          sudo apt-get install -y xvfb
          Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
          echo "DISPLAY=:99" >> $GITHUB_ENV
          
      - name: Test
        run: |
          pushd tools/vscode
          yarn test

      - name: Upload extension to Actions Artifact
        uses: actions/upload-artifact@v4
        with:
          name: inspect-vscode
          path: "tools/vscode/inspect*.vsix"