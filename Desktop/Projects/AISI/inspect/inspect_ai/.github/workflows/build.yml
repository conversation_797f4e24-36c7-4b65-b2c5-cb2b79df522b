name: Build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
      - "release/**"
  workflow_dispatch:

jobs:
  ruff:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]
    steps:
      - uses: actions/checkout@v4
      - name: Lint with Ruff
        uses: chartboost/ruff-action@v1
      - name: Format with Ruff
        uses: chartboost/ruff-action@v1
        with:
          args: "format --check"

  mypy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt', '**/pyproject.toml') }}-${{ matrix.python-version }}
          restore-keys: |
            ${{ runner.os }}-pip-
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install .[dev]
      - name: Run mypy
        run: |
          mypy .

  pre-commit:
    runs-on: ubuntu-latest
    env:
      SKIP: "ruff"
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: pip
      - uses: pre-commit/action@v3.0.1

  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt', '**/pyproject.toml') }}-${{ matrix.python-version }}
          restore-keys: |
            ${{ runner.os }}-pip-
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install .[dev]
      - name: Test with pytest
        run: |
          pytest -rA --doctest-modules --color=yes --cov=inspect_ai

  package:
    name: Build & inspect the package.
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

        # Without this step, the build fails due to these static assets
        # being duplicated into the `dist` folder.
      - name: Delete knowingly duplicated files
        run: rm src/inspect_ai/_view/www/favicon.svg

      - uses: hynek/build-and-inspect-python-package@v2
