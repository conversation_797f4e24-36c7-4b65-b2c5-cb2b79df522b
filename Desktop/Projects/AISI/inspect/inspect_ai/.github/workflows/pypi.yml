name: Publish to PyPI

on:
  workflow_dispatch:
    inputs:
      publish-release:
        description: "Production Release"
        required: false
        type: boolean
        default: false

jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest
    environment: pypi
    strategy:
      fail-fast: false
    permissions:
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install pypa/build
        run: >-
          python3 -m
          pip install
          build
          --user
      - name: Build
        run: python -m build
      - name: Publish package to TestPyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        if: ${{ ! inputs.publish-release }}
        with:
          repository-url: https://test.pypi.org/legacy/
      - name: Publish package to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        if: ${{ inputs.publish-release }}
