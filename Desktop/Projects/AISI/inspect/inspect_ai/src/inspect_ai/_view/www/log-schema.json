{"$defs": {"ApprovalEvent": {"description": "Tool approval.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "approval", "default": "approval", "title": "Event", "type": "string"}, "message": {"title": "Message", "type": "string"}, "call": {"$ref": "#/$defs/ToolCall"}, "view": {"anyOf": [{"$ref": "#/$defs/ToolCallView"}, {"type": "null"}], "default": null}, "approver": {"title": "Approver", "type": "string"}, "decision": {"enum": ["approve", "modify", "reject", "escalate", "terminate"], "title": "Decision", "type": "string"}, "modified": {"anyOf": [{"$ref": "#/$defs/ToolCall"}, {"type": "null"}], "default": null}, "explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Explanation"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "message", "call", "view", "approver", "decision", "modified", "explanation"], "title": "ApprovalEvent", "type": "object", "additionalProperties": false}, "ApprovalPolicyConfig": {"properties": {"approvers": {"items": {"$ref": "#/$defs/ApproverPolicyConfig"}, "title": "Approvers", "type": "array"}}, "required": ["approvers"], "title": "ApprovalPolicyConfig", "type": "object", "additionalProperties": false}, "ApproverPolicyConfig": {"additionalProperties": false, "description": "Configuration format for approver policies.\n\nFor example, here is a configuration in YAML:\n\n```yaml\napprovers:\n  - name: human\n    tools: web_browser*, bash, pyhton\n    choices: [approve, reject]\n\n  - name: auto\n    tools: *\n    decision: approve\n```", "properties": {"name": {"title": "Name", "type": "string"}, "tools": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "title": "Tools"}, "params": {"additionalProperties": true, "title": "Params", "type": "object"}}, "required": ["name", "tools", "params"], "title": "ApproverPolicyConfig", "type": "object"}, "ChatCompletionChoice": {"description": "Choice generated for completion.", "properties": {"message": {"$ref": "#/$defs/ChatMessageAssistant"}, "stop_reason": {"default": "unknown", "enum": ["stop", "max_tokens", "model_length", "tool_calls", "content_filter", "unknown"], "title": "Stop Reason", "type": "string"}, "logprobs": {"anyOf": [{"$ref": "#/$defs/Logprobs"}, {"type": "null"}], "default": null}}, "required": ["message", "stop_reason", "logprobs"], "title": "ChatCompletionChoice", "type": "object", "additionalProperties": false}, "ChatMessageAssistant": {"description": "Assistant chat message.", "properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Id"}, "content": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}]}, "type": "array"}], "title": "Content"}, "source": {"anyOf": [{"enum": ["input", "generate"], "type": "string"}, {"type": "null"}], "default": null, "title": "Source"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "role": {"const": "assistant", "default": "assistant", "title": "Role", "type": "string"}, "tool_calls": {"anyOf": [{"items": {"$ref": "#/$defs/ToolCall"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tool Calls"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Model"}}, "required": ["id", "content", "source", "metadata", "internal", "role", "tool_calls", "model"], "title": "ChatMessageAssistant", "type": "object", "additionalProperties": false}, "ChatMessageSystem": {"description": "System chat message.", "properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Id"}, "content": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}]}, "type": "array"}], "title": "Content"}, "source": {"anyOf": [{"enum": ["input", "generate"], "type": "string"}, {"type": "null"}], "default": null, "title": "Source"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "role": {"const": "system", "default": "system", "title": "Role", "type": "string"}}, "required": ["id", "content", "source", "metadata", "internal", "role"], "title": "ChatMessageSystem", "type": "object", "additionalProperties": false}, "ChatMessageTool": {"description": "Tool chat message.", "properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Id"}, "content": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}]}, "type": "array"}], "title": "Content"}, "source": {"anyOf": [{"enum": ["input", "generate"], "type": "string"}, {"type": "null"}], "default": null, "title": "Source"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "role": {"const": "tool", "default": "tool", "title": "Role", "type": "string"}, "tool_call_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tool Call Id"}, "function": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Function"}, "error": {"anyOf": [{"$ref": "#/$defs/ToolCallError"}, {"type": "null"}], "default": null}}, "required": ["id", "content", "source", "metadata", "internal", "role", "tool_call_id", "function", "error"], "title": "ChatMessageTool", "type": "object", "additionalProperties": false}, "ChatMessageUser": {"description": "User chat message.", "properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Id"}, "content": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}]}, "type": "array"}], "title": "Content"}, "source": {"anyOf": [{"enum": ["input", "generate"], "type": "string"}, {"type": "null"}], "default": null, "title": "Source"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "role": {"const": "user", "default": "user", "title": "Role", "type": "string"}, "tool_call_id": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tool Call Id"}}, "required": ["id", "content", "source", "metadata", "internal", "role", "tool_call_id"], "title": "ChatMessageUser", "type": "object", "additionalProperties": false}, "ContentAudio": {"description": "Audio content.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "audio", "default": "audio", "title": "Type", "type": "string"}, "audio": {"title": "Audio", "type": "string"}, "format": {"enum": ["wav", "mp3"], "title": "Format", "type": "string"}}, "required": ["internal", "type", "audio", "format"], "title": "ContentAudio", "type": "object", "additionalProperties": false}, "ContentCitation": {"description": "A generic content citation.", "properties": {"cited_text": {"anyOf": [{"type": "string"}, {"additionalItems": false, "items": [{"type": "integer"}, {"type": "integer"}], "maxItems": 2, "minItems": 2, "type": "array"}, {"type": "null"}], "default": null, "title": "Cited Text"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "internal": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Internal"}, "type": {"const": "content", "default": "content", "title": "Type", "type": "string"}}, "title": "ContentCitation", "type": "object", "required": ["cited_text", "title", "internal", "type"], "additionalProperties": false}, "ContentData": {"description": "Model internal.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "data", "default": "data", "title": "Type", "type": "string"}, "data": {"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "title": "Data", "type": "object"}}, "required": ["internal", "type", "data"], "title": "ContentData", "type": "object", "additionalProperties": false}, "ContentImage": {"description": "Image content.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "image", "default": "image", "title": "Type", "type": "string"}, "image": {"title": "Image", "type": "string"}, "detail": {"default": "auto", "enum": ["auto", "low", "high"], "title": "Detail", "type": "string"}}, "required": ["internal", "type", "image", "detail"], "title": "ContentImage", "type": "object", "additionalProperties": false}, "ContentReasoning": {"description": "Reasoning content.\n\nSee the specification for [thinking blocks](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking#understanding-thinking-blocks) for Claude models.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "reasoning", "default": "reasoning", "title": "Type", "type": "string"}, "reasoning": {"title": "Reasoning", "type": "string"}, "signature": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Signature"}, "redacted": {"default": false, "title": "Redacted", "type": "boolean"}}, "required": ["internal", "type", "reasoning", "signature", "redacted"], "title": "ContentReasoning", "type": "object", "additionalProperties": false}, "ContentText": {"description": "Text content.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "text", "default": "text", "title": "Type", "type": "string"}, "text": {"title": "Text", "type": "string"}, "refusal": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Refusal"}, "citations": {"anyOf": [{"items": {"discriminator": {"mapping": {"content": "#/$defs/ContentCitation", "document": "#/$defs/DocumentCitation", "url": "#/$defs/UrlCitation"}, "propertyName": "type"}, "oneOf": [{"$ref": "#/$defs/ContentCitation"}, {"$ref": "#/$defs/DocumentCitation"}, {"$ref": "#/$defs/UrlCitation"}]}, "type": "array"}, {"type": "null"}], "default": null, "title": "Citations"}}, "required": ["internal", "type", "text", "refusal", "citations"], "title": "ContentText", "type": "object", "additionalProperties": false}, "ContentVideo": {"description": "Video content.", "properties": {"internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "type": {"const": "video", "default": "video", "title": "Type", "type": "string"}, "video": {"title": "Video", "type": "string"}, "format": {"enum": ["mp4", "mpeg", "mov"], "title": "Format", "type": "string"}}, "required": ["internal", "type", "video", "format"], "title": "ContentVideo", "type": "object", "additionalProperties": false}, "DocumentCitation": {"description": "A citation that refers to a page range in a document.", "properties": {"cited_text": {"anyOf": [{"type": "string"}, {"additionalItems": false, "items": [{"type": "integer"}, {"type": "integer"}], "maxItems": 2, "minItems": 2, "type": "array"}, {"type": "null"}], "default": null, "title": "Cited Text"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "internal": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Internal"}, "type": {"const": "document", "default": "document", "title": "Type", "type": "string"}, "range": {"anyOf": [{"$ref": "#/$defs/DocumentRange"}, {"type": "null"}], "default": null}}, "title": "DocumentCitation", "type": "object", "required": ["cited_text", "title", "internal", "type", "range"], "additionalProperties": false}, "DocumentRange": {"description": "A range specifying a section of a document.", "properties": {"type": {"enum": ["block", "page", "char"], "title": "Type", "type": "string"}, "start_index": {"title": "Start Index", "type": "integer"}, "end_index": {"title": "End Index", "type": "integer"}}, "required": ["type", "start_index", "end_index"], "title": "DocumentRange", "type": "object", "additionalProperties": false}, "ErrorEvent": {"description": "Event with sample error.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "error", "default": "error", "title": "Event", "type": "string"}, "error": {"$ref": "#/$defs/EvalError"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "error"], "title": "ErrorEvent", "type": "object", "additionalProperties": false}, "EvalConfig": {"description": "Configuration used for evaluation.", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"maxItems": 2, "minItems": 2, "prefixItems": [{"type": "integer"}, {"type": "integer"}], "type": "array"}, {"type": "null"}], "default": null, "title": "Limit"}, "sample_id": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"items": {"type": "string"}, "type": "array"}, {"items": {"type": "integer"}, "type": "array"}, {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array"}, {"type": "null"}], "default": null, "title": "Sample Id"}, "epochs": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Epochs"}, "epochs_reducer": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Epochs Reducer"}, "approval": {"anyOf": [{"$ref": "#/$defs/ApprovalPolicyConfig"}, {"type": "null"}], "default": null}, "fail_on_error": {"anyOf": [{"type": "boolean"}, {"type": "number"}, {"type": "null"}], "default": null, "title": "Fail On <PERSON>r"}, "retry_on_error": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Retry On Error"}, "message_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Message Limit"}, "token_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Token Limit"}, "time_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Time Limit"}, "working_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Working Limit"}, "max_samples": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "max_tasks": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Tasks"}, "max_subprocesses": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Subprocesses"}, "max_sandboxes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Sandboxes"}, "sandbox_cleanup": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Sandbox Cleanup"}, "log_samples": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Log Samples"}, "log_realtime": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Log Realtime"}, "log_images": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Log Images"}, "log_buffer": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Log Buffer"}, "log_shared": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Log Shared"}, "score_display": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Score Display"}}, "title": "EvalConfig", "type": "object", "required": ["limit", "sample_id", "epochs", "epochs_reducer", "approval", "fail_on_error", "retry_on_error", "message_limit", "token_limit", "time_limit", "working_limit", "max_samples", "max_tasks", "max_subprocesses", "max_sandboxes", "sandbox_cleanup", "log_samples", "log_realtime", "log_images", "log_buffer", "log_shared", "score_display"], "additionalProperties": false}, "EvalDataset": {"description": "Dataset used for evaluation.", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Location"}, "samples": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "sample_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"items": {"type": "integer"}, "type": "array"}, {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array"}, {"type": "null"}], "default": null, "title": "Sample Ids"}, "shuffled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Shuffled"}}, "title": "EvalDataset", "type": "object", "required": ["name", "location", "samples", "sample_ids", "shuffled"], "additionalProperties": false}, "EvalError": {"description": "Eval error details.", "properties": {"message": {"title": "Message", "type": "string"}, "traceback": {"title": "<PERSON><PERSON>", "type": "string"}, "traceback_ansi": {"title": "<PERSON><PERSON>", "type": "string"}}, "required": ["message", "traceback", "traceback_ansi"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": false}, "EvalMetric": {"description": "Metric for evaluation score.", "properties": {"name": {"title": "Name", "type": "string"}, "value": {"anyOf": [{"type": "integer"}, {"type": "number"}], "title": "Value"}, "params": {"additionalProperties": true, "title": "Params", "type": "object"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "required": ["name", "value", "params", "metadata"], "title": "EvalMetric", "type": "object", "additionalProperties": false}, "EvalMetricDefinition": {"properties": {"name": {"title": "Name", "type": "string"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Options"}}, "required": ["name", "options"], "title": "EvalMetricDefinition", "type": "object", "additionalProperties": false}, "EvalModelConfig": {"description": "Model config.", "properties": {"model": {"title": "Model", "type": "string"}, "config": {"$ref": "#/$defs/GenerateConfig"}, "base_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Base Url"}, "args": {"additionalProperties": true, "title": "<PERSON><PERSON><PERSON>", "type": "object"}}, "required": ["model", "config", "base_url", "args"], "title": "EvalModelConfig", "type": "object", "additionalProperties": false}, "EvalPlan": {"description": "Plan (solvers) used in evaluation.", "properties": {"name": {"default": "plan", "title": "Name", "type": "string"}, "steps": {"default": [], "items": {"$ref": "#/$defs/EvalPlanStep"}, "title": "Steps", "type": "array"}, "finish": {"anyOf": [{"$ref": "#/$defs/EvalPlanStep"}, {"type": "null"}], "default": null}, "config": {"$ref": "#/$defs/GenerateConfig", "default": {"max_retries": null, "timeout": null, "max_connections": null, "system_message": null, "max_tokens": null, "top_p": null, "temperature": null, "stop_seqs": null, "best_of": null, "frequency_penalty": null, "presence_penalty": null, "logit_bias": null, "seed": null, "top_k": null, "num_choices": null, "logprobs": null, "top_logprobs": null, "parallel_tool_calls": null, "internal_tools": null, "max_tool_output": null, "cache_prompt": null, "reasoning_effort": null, "reasoning_tokens": null, "reasoning_summary": null, "reasoning_history": null, "response_schema": null, "extra_body": null}}}, "title": "EvalPlan", "type": "object", "required": ["name", "steps", "finish", "config"], "additionalProperties": false}, "EvalPlanStep": {"description": "Solver step.", "properties": {"solver": {"title": "Solver", "type": "string"}, "params": {"additionalProperties": true, "title": "Params", "type": "object"}}, "required": ["solver", "params"], "title": "EvalPlanStep", "type": "object", "additionalProperties": false}, "EvalResults": {"description": "Scoring results from evaluation.", "properties": {"total_samples": {"default": 0, "title": "Total Samples", "type": "integer"}, "completed_samples": {"default": 0, "title": "Completed Samples", "type": "integer"}, "scores": {"default": [], "items": {"$ref": "#/$defs/EvalScore"}, "title": "Scores", "type": "array"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "title": "EvalResults", "type": "object", "required": ["total_samples", "completed_samples", "scores", "metadata"], "additionalProperties": false}, "EvalRevision": {"description": "Git revision for evaluation.", "properties": {"type": {"const": "git", "title": "Type", "type": "string"}, "origin": {"title": "Origin", "type": "string"}, "commit": {"title": "Commit", "type": "string"}}, "required": ["type", "origin", "commit"], "title": "EvalRevision", "type": "object", "additionalProperties": false}, "EvalSample": {"description": "Sample from evaluation task.", "properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "string"}], "title": "Id"}, "epoch": {"title": "Epoch", "type": "integer"}, "input": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ChatMessageSystem"}, {"$ref": "#/$defs/ChatMessageUser"}, {"$ref": "#/$defs/ChatMessageAssistant"}, {"$ref": "#/$defs/ChatMessageTool"}]}, "type": "array"}], "title": "Input"}, "choices": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Choices"}, "target": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "title": "Target"}, "sandbox": {"anyOf": [{"$ref": "#/$defs/SandboxEnvironmentSpec"}, {"type": "null"}], "default": null}, "files": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Files"}, "setup": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Setup"}, "messages": {"items": {"anyOf": [{"$ref": "#/$defs/ChatMessageSystem"}, {"$ref": "#/$defs/ChatMessageUser"}, {"$ref": "#/$defs/ChatMessageAssistant"}, {"$ref": "#/$defs/ChatMessageTool"}]}, "title": "Messages", "type": "array"}, "output": {"$ref": "#/$defs/ModelOutput"}, "scores": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/Score"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Scores"}, "metadata": {"additionalProperties": true, "title": "<PERSON><PERSON><PERSON>", "type": "object"}, "store": {"additionalProperties": true, "title": "Store", "type": "object"}, "events": {"items": {"anyOf": [{"$ref": "#/$defs/SampleInitEvent"}, {"$ref": "#/$defs/SampleLimitEvent"}, {"$ref": "#/$defs/SandboxEvent"}, {"$ref": "#/$defs/StateEvent"}, {"$ref": "#/$defs/StoreEvent"}, {"$ref": "#/$defs/ModelEvent"}, {"$ref": "#/$defs/ToolEvent"}, {"$ref": "#/$defs/ApprovalEvent"}, {"$ref": "#/$defs/InputEvent"}, {"$ref": "#/$defs/ScoreEvent"}, {"$ref": "#/$defs/ErrorEvent"}, {"$ref": "#/$defs/LoggerEvent"}, {"$ref": "#/$defs/InfoEvent"}, {"$ref": "#/$defs/SpanBeginEvent"}, {"$ref": "#/$defs/SpanEndEvent"}, {"$ref": "#/$defs/StepEvent"}, {"$ref": "#/$defs/SubtaskEvent"}]}, "title": "Events", "type": "array"}, "model_usage": {"additionalProperties": {"$ref": "#/$defs/ModelUsage"}, "title": "Model Usage", "type": "object"}, "total_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Total Time"}, "working_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Working Time"}, "uuid": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "error": {"anyOf": [{"$ref": "#/$defs/EvalError"}, {"type": "null"}], "default": null}, "error_retries": {"anyOf": [{"items": {"$ref": "#/$defs/EvalError"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Error <PERSON>"}, "attachments": {"additionalProperties": {"type": "string"}, "title": "Attachments", "type": "object"}, "limit": {"anyOf": [{"$ref": "#/$defs/EvalSampleLimit"}, {"type": "null"}], "default": null}}, "required": ["id", "epoch", "input", "choices", "target", "sandbox", "files", "setup", "messages", "output", "scores", "metadata", "store", "events", "model_usage", "total_time", "working_time", "uuid", "error", "error_retries", "attachments", "limit"], "title": "EvalSample", "type": "object", "additionalProperties": false}, "EvalSampleLimit": {"description": "Limit encountered by sample.", "properties": {"type": {"enum": ["context", "time", "working", "message", "token", "operator", "custom"], "title": "Type", "type": "string"}, "limit": {"title": "Limit", "type": "number"}}, "required": ["type", "limit"], "title": "EvalSampleLimit", "type": "object", "additionalProperties": false}, "EvalSampleReductions": {"description": "Score reductions.", "properties": {"scorer": {"title": "Scorer", "type": "string"}, "reducer": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Reducer"}, "samples": {"items": {"$ref": "#/$defs/EvalSampleScore"}, "title": "<PERSON><PERSON>", "type": "array"}}, "required": ["scorer", "reducer", "samples"], "title": "EvalSampleReductions", "type": "object", "additionalProperties": false}, "EvalSampleScore": {"description": "Score and sample_id scored.", "properties": {"value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}]}, "type": "array"}, {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}]}, "type": "object"}], "title": "Value"}, "answer": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Answer"}, "explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Explanation"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "sample_id": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "null"}], "default": null, "title": "Sample Id"}}, "required": ["value", "answer", "explanation", "metadata", "sample_id"], "title": "EvalSampleScore", "type": "object", "additionalProperties": false}, "EvalScore": {"description": "Score for evaluation task.", "properties": {"name": {"title": "Name", "type": "string"}, "scorer": {"title": "Scorer", "type": "string"}, "reducer": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Reducer"}, "params": {"additionalProperties": true, "title": "Params", "type": "object"}, "metrics": {"additionalProperties": {"$ref": "#/$defs/EvalMetric"}, "title": "Metrics", "type": "object"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "required": ["name", "scorer", "reducer", "params", "metrics", "metadata"], "title": "EvalScore", "type": "object", "additionalProperties": false}, "EvalScorer": {"properties": {"name": {"title": "Name", "type": "string"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Options"}, "metrics": {"anyOf": [{"items": {"anyOf": [{"$ref": "#/$defs/EvalMetricDefinition"}, {"additionalProperties": {"items": {"$ref": "#/$defs/EvalMetricDefinition"}, "type": "array"}, "type": "object"}]}, "type": "array"}, {"additionalProperties": {"items": {"$ref": "#/$defs/EvalMetricDefinition"}, "type": "array"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Metrics"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "required": ["name", "options", "metrics", "metadata"], "title": "EvalScorer", "type": "object", "additionalProperties": false}, "EvalSpec": {"description": "Eval target and configuration.", "properties": {"eval_id": {"title": "Eval Id", "type": "string"}, "run_id": {"title": "Run Id", "type": "string"}, "created": {"title": "Created", "type": "string"}, "task": {"title": "Task", "type": "string"}, "task_id": {"title": "Task Id", "type": "string"}, "task_version": {"anyOf": [{"type": "integer"}, {"type": "string"}], "default": 0, "title": "Task Version"}, "task_file": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Task File"}, "task_registry_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Task Registry Name"}, "task_attribs": {"additionalProperties": true, "title": "Task Attribs", "type": "object"}, "task_args": {"additionalProperties": true, "title": "Task Args", "type": "object"}, "task_args_passed": {"additionalProperties": true, "title": "Task Args Passed", "type": "object"}, "solver": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Solver"}, "solver_args": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Solver Args"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tags"}, "dataset": {"$ref": "#/$defs/EvalDataset"}, "sandbox": {"anyOf": [{"$ref": "#/$defs/SandboxEnvironmentSpec"}, {"type": "null"}], "default": null}, "model": {"title": "Model", "type": "string"}, "model_generate_config": {"$ref": "#/$defs/GenerateConfig"}, "model_base_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Model Base Url"}, "model_args": {"additionalProperties": true, "title": "Model Args", "type": "object"}, "model_roles": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/EvalModelConfig"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Model Roles"}, "config": {"$ref": "#/$defs/EvalConfig"}, "revision": {"anyOf": [{"$ref": "#/$defs/EvalRevision"}, {"type": "null"}], "default": null}, "packages": {"additionalProperties": {"type": "string"}, "title": "Packages", "type": "object"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "scorers": {"anyOf": [{"items": {"$ref": "#/$defs/EvalScorer"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Scorers"}, "metrics": {"anyOf": [{"items": {"$ref": "#/$defs/EvalMetricDefinition"}, "type": "array"}, {"additionalProperties": {"items": {"$ref": "#/$defs/EvalMetricDefinition"}, "type": "array"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Metrics"}}, "required": ["eval_id", "run_id", "created", "task", "task_id", "task_version", "task_file", "task_registry_name", "task_attribs", "task_args", "task_args_passed", "solver", "solver_args", "tags", "dataset", "sandbox", "model", "model_generate_config", "model_base_url", "model_args", "model_roles", "config", "revision", "packages", "metadata", "scorers", "metrics"], "title": "EvalSpec", "type": "object", "additionalProperties": false}, "EvalStats": {"description": "Timing and usage statistics.", "properties": {"started_at": {"title": "Started At", "type": "string"}, "completed_at": {"title": "Completed At", "type": "string"}, "model_usage": {"additionalProperties": {"$ref": "#/$defs/ModelUsage"}, "title": "Model Usage", "type": "object"}}, "title": "EvalStats", "type": "object", "required": ["started_at", "completed_at", "model_usage"], "additionalProperties": false}, "GenerateConfig": {"description": "Model generation options.", "properties": {"max_retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Retries"}, "timeout": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Timeout"}, "max_connections": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Connections"}, "system_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "System Message"}, "max_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "top_p": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Top P"}, "temperature": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Temperature"}, "stop_seqs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Stop Seqs"}, "best_of": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Best Of"}, "frequency_penalty": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Frequency Penalty"}, "presence_penalty": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Presence Penalty"}, "logit_bias": {"anyOf": [{"additionalProperties": {"type": "number"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Logit Bias"}, "seed": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Seed"}, "top_k": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Top K"}, "num_choices": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Num Choices"}, "logprobs": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Logprobs"}, "top_logprobs": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Top Logprobs"}, "parallel_tool_calls": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Parallel Tool Calls"}, "internal_tools": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Internal Tools"}, "max_tool_output": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Max Tool Output"}, "cache_prompt": {"anyOf": [{"const": "auto", "type": "string"}, {"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON> Prompt"}, "reasoning_effort": {"anyOf": [{"enum": ["low", "medium", "high"], "type": "string"}, {"type": "null"}], "default": null, "title": "Reasoning Effort"}, "reasoning_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Reasoning Tokens"}, "reasoning_summary": {"anyOf": [{"enum": ["concise", "detailed", "auto"], "type": "string"}, {"type": "null"}], "default": null, "title": "Reasoning Summary"}, "reasoning_history": {"anyOf": [{"enum": ["none", "all", "last", "auto"], "type": "string"}, {"type": "null"}], "default": null, "title": "Reasoning History"}, "response_schema": {"anyOf": [{"$ref": "#/$defs/ResponseSchema"}, {"type": "null"}], "default": null}, "extra_body": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Extra Body"}}, "title": "GenerateConfig", "type": "object", "required": ["max_retries", "timeout", "max_connections", "system_message", "max_tokens", "top_p", "temperature", "stop_seqs", "best_of", "frequency_penalty", "presence_penalty", "logit_bias", "seed", "top_k", "num_choices", "logprobs", "top_logprobs", "parallel_tool_calls", "internal_tools", "max_tool_output", "cache_prompt", "reasoning_effort", "reasoning_tokens", "reasoning_summary", "reasoning_history", "response_schema", "extra_body"], "additionalProperties": false}, "InfoEvent": {"description": "Event with custom info/data.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "info", "default": "info", "title": "Event", "type": "string"}, "source": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Source"}, "data": {"$ref": "#/$defs/JsonValue"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "source", "data"], "title": "InfoEvent", "type": "object", "additionalProperties": false}, "InputEvent": {"description": "Input screen interaction.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "input", "default": "input", "title": "Event", "type": "string"}, "input": {"title": "Input", "type": "string"}, "input_ansi": {"title": "Input Ansi", "type": "string"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "input", "input_ansi"], "title": "InputEvent", "type": "object", "additionalProperties": false}, "JSONSchema": {"description": "JSON Schema for type.", "properties": {"type": {"anyOf": [{"enum": ["string", "integer", "number", "boolean", "array", "object", "null"], "type": "string"}, {"type": "null"}], "default": null, "title": "Type"}, "format": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Format"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "default": {"default": null, "title": "<PERSON><PERSON><PERSON>"}, "enum": {"anyOf": [{"items": {}, "type": "array"}, {"type": "null"}], "default": null, "title": "Enum"}, "items": {"anyOf": [{"$ref": "#/$defs/JSONSchema"}, {"type": "null"}], "default": null}, "properties": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/JSONSchema"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Properties"}, "additionalProperties": {"anyOf": [{"$ref": "#/$defs/JSONSchema"}, {"type": "boolean"}, {"type": "null"}], "default": null, "title": "Additionalproperties"}, "anyOf": {"anyOf": [{"items": {"$ref": "#/$defs/JSONSchema"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Anyof"}, "required": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Required"}}, "title": "JSONSchema", "type": "object", "required": ["type", "format", "description", "default", "enum", "items", "properties", "additionalProperties", "anyOf", "required"], "additionalProperties": false}, "JsonChange": {"description": "Describes a change to data using JSON Patch format.", "properties": {"op": {"enum": ["remove", "add", "replace", "move", "test", "copy"], "title": "Op", "type": "string"}, "path": {"title": "Path", "type": "string"}, "from": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From"}, "value": {"$ref": "#/$defs/JsonValue", "default": null}, "replaced": {"$ref": "#/$defs/JsonValue", "default": null}}, "required": ["op", "path", "from", "value", "replaced"], "title": "JsonChange", "type": "object", "additionalProperties": false}, "JsonValue": {}, "LoggerEvent": {"description": "Log message recorded with Python logger.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "logger", "default": "logger", "title": "Event", "type": "string"}, "message": {"$ref": "#/$defs/LoggingMessage"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "message"], "title": "LoggerEvent", "type": "object", "additionalProperties": false}, "LoggingMessage": {"description": "Message written to Python log.", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "level": {"enum": ["debug", "trace", "http", "sandbox", "info", "warning", "error", "critical"], "title": "Level", "type": "string"}, "message": {"title": "Message", "type": "string"}, "created": {"title": "Created", "type": "number"}, "filename": {"default": "unknown", "title": "Filename", "type": "string"}, "module": {"default": "unknown", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "lineno": {"default": 0, "title": "Lineno", "type": "integer"}}, "required": ["name", "level", "message", "created", "filename", "module", "lineno"], "title": "LoggingMessage", "type": "object", "additionalProperties": false}, "Logprob": {"description": "Log probability for a token.", "properties": {"token": {"title": "Token", "type": "string"}, "logprob": {"title": "Logprob", "type": "number"}, "bytes": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bytes"}, "top_logprobs": {"anyOf": [{"items": {"$ref": "#/$defs/TopLogprob"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Top Logprobs"}}, "required": ["token", "logprob", "bytes", "top_logprobs"], "title": "Logprob", "type": "object", "additionalProperties": false}, "Logprobs": {"description": "Log probability information for a completion choice.", "properties": {"content": {"items": {"$ref": "#/$defs/Logprob"}, "title": "Content", "type": "array"}}, "required": ["content"], "title": "Logprobs", "type": "object", "additionalProperties": false}, "ModelCall": {"description": "Model call (raw request/response data).", "properties": {"request": {"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "title": "Request", "type": "object"}, "response": {"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "title": "Response", "type": "object"}, "time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Time"}}, "required": ["request", "response", "time"], "title": "ModelCall", "type": "object", "additionalProperties": false}, "ModelEvent": {"description": "Call to a language model.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "model", "default": "model", "title": "Event", "type": "string"}, "model": {"title": "Model", "type": "string"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Role"}, "input": {"items": {"anyOf": [{"$ref": "#/$defs/ChatMessageSystem"}, {"$ref": "#/$defs/ChatMessageUser"}, {"$ref": "#/$defs/ChatMessageAssistant"}, {"$ref": "#/$defs/ChatMessageTool"}]}, "title": "Input", "type": "array"}, "tools": {"items": {"$ref": "#/$defs/ToolInfo"}, "title": "Tools", "type": "array"}, "tool_choice": {"anyOf": [{"enum": ["auto", "any", "none"], "type": "string"}, {"$ref": "#/$defs/ToolFunction"}], "title": "Tool Choice"}, "config": {"$ref": "#/$defs/GenerateConfig"}, "output": {"$ref": "#/$defs/ModelOutput"}, "retries": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Retries"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Error"}, "cache": {"anyOf": [{"enum": ["read", "write"], "type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "call": {"anyOf": [{"$ref": "#/$defs/ModelCall"}, {"type": "null"}], "default": null}, "completed": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "title": "Completed"}, "working_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Working Time"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "model", "role", "input", "tools", "tool_choice", "config", "output", "retries", "error", "cache", "call", "completed", "working_time"], "title": "ModelEvent", "type": "object", "additionalProperties": false}, "ModelOutput": {"description": "Output from model generation.", "properties": {"model": {"title": "Model", "type": "string"}, "choices": {"default": [], "items": {"$ref": "#/$defs/ChatCompletionChoice"}, "title": "Choices", "type": "array"}, "usage": {"anyOf": [{"$ref": "#/$defs/ModelUsage"}, {"type": "null"}], "default": null}, "time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Time"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Error"}}, "title": "ModelOutput", "type": "object", "required": ["model", "choices", "usage", "time", "metadata", "error"], "additionalProperties": false}, "ModelUsage": {"description": "Token usage for completion.", "properties": {"input_tokens": {"default": 0, "title": "Input Tokens", "type": "integer"}, "output_tokens": {"default": 0, "title": "Output Tokens", "type": "integer"}, "total_tokens": {"default": 0, "title": "Total Tokens", "type": "integer"}, "input_tokens_cache_write": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Input Tokens Cache Write"}, "input_tokens_cache_read": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Input Tokens Cache Read"}, "reasoning_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Reasoning Tokens"}}, "title": "ModelUsage", "type": "object", "required": ["input_tokens", "output_tokens", "total_tokens", "input_tokens_cache_write", "input_tokens_cache_read", "reasoning_tokens"], "additionalProperties": false}, "ResponseSchema": {"description": "Schema for model response when using Structured Output.", "properties": {"name": {"title": "Name", "type": "string"}, "json_schema": {"$ref": "#/$defs/JSONSchema"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "strict": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Strict"}}, "required": ["name", "json_schema", "description", "strict"], "title": "ResponseSchema", "type": "object", "additionalProperties": false}, "Sample": {"description": "<PERSON><PERSON> for an evaluation task.", "properties": {"input": {"anyOf": [{"type": "string"}, {"items": {"anyOf": [{"$ref": "#/$defs/ChatMessageSystem"}, {"$ref": "#/$defs/ChatMessageUser"}, {"$ref": "#/$defs/ChatMessageAssistant"}, {"$ref": "#/$defs/ChatMessageTool"}]}, "type": "array"}], "title": "Input"}, "choices": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Choices"}, "target": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}], "title": "Target"}, "id": {"anyOf": [{"type": "integer"}, {"type": "string"}, {"type": "null"}], "default": null, "title": "Id"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "sandbox": {"anyOf": [{"$ref": "#/$defs/SandboxEnvironmentSpec"}, {"type": "null"}], "default": null}, "files": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Files"}, "setup": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Setup"}}, "required": ["input", "choices", "target", "id", "metadata", "sandbox", "files", "setup"], "title": "<PERSON><PERSON>", "type": "object", "additionalProperties": false}, "SampleInitEvent": {"description": "Beginning of processing a Sample.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "sample_init", "default": "sample_init", "title": "Event", "type": "string"}, "sample": {"$ref": "#/$defs/Sample"}, "state": {"$ref": "#/$defs/JsonValue"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "sample", "state"], "title": "SampleInitEvent", "type": "object", "additionalProperties": false}, "SampleLimitEvent": {"description": "The sample was unable to finish processing due to a limit", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "sample_limit", "default": "sample_limit", "title": "Event", "type": "string"}, "type": {"enum": ["message", "time", "working", "token", "operator", "custom"], "title": "Type", "type": "string"}, "message": {"title": "Message", "type": "string"}, "limit": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Limit"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "type", "message", "limit"], "title": "SampleLimitEvent", "type": "object", "additionalProperties": false}, "SandboxEnvironmentSpec": {"description": "Specification of a SandboxEnvironment.", "properties": {"type": {"title": "Type", "type": "string"}, "config": {"default": null, "title": "Config"}}, "required": ["type", "config"], "title": "SandboxEnvironmentSpec", "type": "object", "additionalProperties": false}, "SandboxEvent": {"description": "Sandbox execution or I/O", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "sandbox", "default": "sandbox", "title": "Event", "type": "string"}, "action": {"enum": ["exec", "read_file", "write_file"], "title": "Action", "type": "string"}, "cmd": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Cmd"}, "options": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Options"}, "file": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "File"}, "input": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Input"}, "result": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Result"}, "output": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Output"}, "completed": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "title": "Completed"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "action", "cmd", "options", "file", "input", "result", "output", "completed"], "title": "SandboxEvent", "type": "object", "additionalProperties": false}, "Score": {"description": "Score generated by a scorer.", "properties": {"value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}]}, "type": "array"}, {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"type": "null"}]}, "type": "object"}], "title": "Value"}, "answer": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Answer"}, "explanation": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Explanation"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "required": ["value", "answer", "explanation", "metadata"], "title": "Score", "type": "object", "additionalProperties": false}, "ScoreEvent": {"description": "Event with score.\n\nCan be the final score for a `Sample`, or can be an intermediate score\nresulting from a call to `score`.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "score", "default": "score", "title": "Event", "type": "string"}, "score": {"$ref": "#/$defs/Score"}, "target": {"anyOf": [{"type": "string"}, {"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Target"}, "intermediate": {"default": false, "title": "Intermediate", "type": "boolean"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "score", "target", "intermediate"], "title": "ScoreEvent", "type": "object", "additionalProperties": false}, "SpanBeginEvent": {"description": "<PERSON> the beginning of a transcript span.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "span_begin", "default": "span_begin", "title": "Event", "type": "string"}, "id": {"title": "Id", "type": "string"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Parent Id"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Type"}, "name": {"title": "Name", "type": "string"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "id", "parent_id", "type", "name"], "title": "SpanBeginEvent", "type": "object", "additionalProperties": false}, "SpanEndEvent": {"description": "Mark the end of a transcript span.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "span_end", "default": "span_end", "title": "Event", "type": "string"}, "id": {"title": "Id", "type": "string"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "id"], "title": "SpanEndEvent", "type": "object", "additionalProperties": false}, "StateEvent": {"description": "Change to the current `TaskState`", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "state", "default": "state", "title": "Event", "type": "string"}, "changes": {"items": {"$ref": "#/$defs/JsonChange"}, "title": "Changes", "type": "array"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "changes"], "title": "StateEvent", "type": "object", "additionalProperties": false}, "StepEvent": {"description": "Step within current sample or subtask.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "step", "default": "step", "title": "Event", "type": "string"}, "action": {"enum": ["begin", "end"], "title": "Action", "type": "string"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Type"}, "name": {"title": "Name", "type": "string"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "action", "type", "name"], "title": "StepEvent", "type": "object", "additionalProperties": false}, "StoreEvent": {"description": "Change to data within the current `Store`.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "store", "default": "store", "title": "Event", "type": "string"}, "changes": {"items": {"$ref": "#/$defs/JsonChange"}, "title": "Changes", "type": "array"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "changes"], "title": "StoreEvent", "type": "object", "additionalProperties": false}, "SubtaskEvent": {"description": "Subtask spawned.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "subtask", "default": "subtask", "title": "Event", "type": "string"}, "name": {"title": "Name", "type": "string"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Type"}, "input": {"additionalProperties": true, "title": "Input", "type": "object"}, "result": {"default": null, "title": "Result"}, "events": {"items": {"anyOf": [{"$ref": "#/$defs/SampleInitEvent"}, {"$ref": "#/$defs/SampleLimitEvent"}, {"$ref": "#/$defs/SandboxEvent"}, {"$ref": "#/$defs/StateEvent"}, {"$ref": "#/$defs/StoreEvent"}, {"$ref": "#/$defs/ModelEvent"}, {"$ref": "#/$defs/ToolEvent"}, {"$ref": "#/$defs/ApprovalEvent"}, {"$ref": "#/$defs/InputEvent"}, {"$ref": "#/$defs/ScoreEvent"}, {"$ref": "#/$defs/ErrorEvent"}, {"$ref": "#/$defs/LoggerEvent"}, {"$ref": "#/$defs/InfoEvent"}, {"$ref": "#/$defs/SpanBeginEvent"}, {"$ref": "#/$defs/SpanEndEvent"}, {"$ref": "#/$defs/StepEvent"}, {"$ref": "#/$defs/SubtaskEvent"}]}, "title": "Events", "type": "array"}, "completed": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "title": "Completed"}, "working_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Working Time"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "name", "type", "input", "result", "events", "completed", "working_time"], "title": "SubtaskEvent", "type": "object", "additionalProperties": false}, "ToolCall": {"properties": {"id": {"title": "Id", "type": "string"}, "function": {"title": "Function", "type": "string"}, "arguments": {"additionalProperties": true, "title": "Arguments", "type": "object"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "parse_error": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "view": {"anyOf": [{"$ref": "#/$defs/ToolCallContent"}, {"type": "null"}], "default": null}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Type"}}, "required": ["id", "function", "arguments", "internal", "parse_error", "view", "type"], "title": "ToolCall", "type": "object", "additionalProperties": false}, "ToolCallContent": {"description": "Content to include in tool call view.", "properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "format": {"enum": ["text", "markdown"], "title": "Format", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["title", "format", "content"], "title": "ToolCallContent", "type": "object", "additionalProperties": false}, "ToolCallError": {"properties": {"type": {"enum": ["parsing", "timeout", "unicode_decode", "permission", "file_not_found", "is_a_directory", "limit", "approval", "unknown", "output_limit"], "title": "Type", "type": "string"}, "message": {"title": "Message", "type": "string"}}, "required": ["type", "message"], "title": "ToolCallError", "type": "object", "additionalProperties": false}, "ToolCallView": {"description": "Custom view of a tool call.\n\nBoth `context` and `call` are optional. If `call` is not specified\nthen the view will default to a syntax highlighted Python function call.", "properties": {"context": {"anyOf": [{"$ref": "#/$defs/ToolCallContent"}, {"type": "null"}], "default": null}, "call": {"anyOf": [{"$ref": "#/$defs/ToolCallContent"}, {"type": "null"}], "default": null}}, "title": "ToolCallView", "type": "object", "required": ["context", "call"], "additionalProperties": false}, "ToolEvent": {"description": "Call to a tool.", "properties": {"span_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Span Id"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "working_start": {"title": "Working Start", "type": "number"}, "pending": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Pending"}, "event": {"const": "tool", "default": "tool", "title": "Event", "type": "string"}, "type": {"const": "function", "default": "function", "title": "Type", "type": "string"}, "id": {"title": "Id", "type": "string"}, "function": {"title": "Function", "type": "string"}, "arguments": {"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "title": "Arguments", "type": "object"}, "internal": {"anyOf": [{"$ref": "#/$defs/JsonValue"}, {"type": "null"}], "default": null}, "view": {"anyOf": [{"$ref": "#/$defs/ToolCallContent"}, {"type": "null"}], "default": null}, "result": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}, {"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}, {"items": {"anyOf": [{"$ref": "#/$defs/ContentText"}, {"$ref": "#/$defs/ContentReasoning"}, {"$ref": "#/$defs/ContentImage"}, {"$ref": "#/$defs/ContentAudio"}, {"$ref": "#/$defs/ContentVideo"}, {"$ref": "#/$defs/ContentData"}]}, "type": "array"}], "title": "Result"}, "truncated": {"anyOf": [{"maxItems": 2, "minItems": 2, "prefixItems": [{"type": "integer"}, {"type": "integer"}], "type": "array"}, {"type": "null"}], "default": null, "title": "Truncated"}, "error": {"anyOf": [{"$ref": "#/$defs/ToolCallError"}, {"type": "null"}], "default": null}, "events": {"items": {"anyOf": [{"$ref": "#/$defs/SampleInitEvent"}, {"$ref": "#/$defs/SampleLimitEvent"}, {"$ref": "#/$defs/SandboxEvent"}, {"$ref": "#/$defs/StateEvent"}, {"$ref": "#/$defs/StoreEvent"}, {"$ref": "#/$defs/ModelEvent"}, {"$ref": "#/$defs/ToolEvent"}, {"$ref": "#/$defs/ApprovalEvent"}, {"$ref": "#/$defs/InputEvent"}, {"$ref": "#/$defs/ScoreEvent"}, {"$ref": "#/$defs/ErrorEvent"}, {"$ref": "#/$defs/LoggerEvent"}, {"$ref": "#/$defs/InfoEvent"}, {"$ref": "#/$defs/SpanBeginEvent"}, {"$ref": "#/$defs/SpanEndEvent"}, {"$ref": "#/$defs/StepEvent"}, {"$ref": "#/$defs/SubtaskEvent"}]}, "title": "Events", "type": "array"}, "completed": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "title": "Completed"}, "working_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Working Time"}, "agent": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Agent"}, "failed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Failed"}}, "required": ["span_id", "timestamp", "working_start", "pending", "event", "type", "id", "function", "arguments", "internal", "view", "result", "truncated", "error", "events", "completed", "working_time", "agent", "failed"], "title": "ToolEvent", "type": "object", "additionalProperties": false}, "ToolFunction": {"properties": {"name": {"title": "Name", "type": "string"}}, "required": ["name"], "title": "ToolFunction", "type": "object", "additionalProperties": false}, "ToolInfo": {"description": "Specification of a tool (JSON Schema compatible)\n\nIf you are implementing a ModelAPI, most LLM libraries can\nbe passed this object (dumped to a dict) directly as a function\nspecification. For example, in the OpenAI provider:\n\n```python\nChatCompletionToolParam(\n    type=\"function\",\n    function=tool.model_dump(exclude_none=True),\n)\n```\n\nIn some cases the field names don't match up exactly. In that case\ncall `model_dump()` on the `parameters` field. For example, in the\nAnthropic provider:\n\n```python\nToolParam(\n    name=tool.name,\n    description=tool.description,\n    input_schema=tool.parameters.model_dump(exclude_none=True),\n)\n```", "properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "parameters": {"$ref": "#/$defs/ToolParams"}, "options": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Options"}}, "required": ["name", "description", "parameters", "options"], "title": "ToolInfo", "type": "object", "additionalProperties": false}, "ToolParams": {"description": "Description of tool parameters object in JSON Schema format.", "properties": {"type": {"const": "object", "default": "object", "title": "Type", "type": "string"}, "properties": {"type": "object", "additionalProperties": {"$ref": "#/$defs/JSONSchema"}, "title": "Properties"}, "required": {"items": {"type": "string"}, "title": "Required", "type": "array"}, "additionalProperties": {"default": false, "title": "Additionalproperties", "type": "boolean"}}, "title": "ToolParams", "type": "object", "required": ["type", "properties", "required", "additionalProperties"], "additionalProperties": false}, "TopLogprob": {"description": "List of the most likely tokens and their log probability, at this token position.", "properties": {"token": {"title": "Token", "type": "string"}, "logprob": {"title": "Logprob", "type": "number"}, "bytes": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Bytes"}}, "required": ["token", "logprob", "bytes"], "title": "TopLogprob", "type": "object", "additionalProperties": false}, "UrlCitation": {"description": "A citation that refers to a URL.", "properties": {"cited_text": {"anyOf": [{"type": "string"}, {"additionalItems": false, "items": [{"type": "integer"}, {"type": "integer"}], "maxItems": 2, "minItems": 2, "type": "array"}, {"type": "null"}], "default": null, "title": "Cited Text"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "internal": {"anyOf": [{"additionalProperties": {"$ref": "#/$defs/JsonValue"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Internal"}, "type": {"const": "url", "default": "url", "title": "Type", "type": "string"}, "url": {"title": "Url", "type": "string"}}, "required": ["cited_text", "title", "internal", "type", "url"], "title": "UrlCitation", "type": "object", "additionalProperties": false}}, "description": "Evaluation log.", "properties": {"version": {"default": 2, "title": "Version", "type": "integer"}, "status": {"default": "started", "enum": ["started", "success", "cancelled", "error"], "title": "Status", "type": "string"}, "eval": {"$ref": "#/$defs/EvalSpec"}, "plan": {"$ref": "#/$defs/EvalPlan"}, "results": {"anyOf": [{"$ref": "#/$defs/EvalResults"}, {"type": "null"}], "default": null}, "stats": {"$ref": "#/$defs/EvalStats"}, "error": {"anyOf": [{"$ref": "#/$defs/EvalError"}, {"type": "null"}], "default": null}, "samples": {"anyOf": [{"items": {"$ref": "#/$defs/EvalSample"}, "type": "array"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "reductions": {"anyOf": [{"items": {"$ref": "#/$defs/EvalSampleReductions"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Reductions"}, "location": {"title": "Location", "type": "string"}}, "required": ["eval"], "title": "EvalLog", "type": "object"}