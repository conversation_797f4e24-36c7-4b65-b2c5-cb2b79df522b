.label {
  margin-right: 0.2em;
  justify-self: end;
}

.navs {
  justify-self: end;
  display: flex;
  flex-direction: columns;
}

.card {
  position: relative;
  background-color: var(--bs-body-bg);
  padding: 0.625rem;
  border: solid 1px var(--bs-light-border-subtle);
  border-radius: var(--bs-border-radius);
}

.cardContent {
  padding: 0;
  display: inherit;
}

.cardContent.hidden {
  display: none;
}

.hidden {
  display: none;
}

.copyLink {
  font-size: 1.2em;
  height: 1em;
  opacity: 0;
  padding-left: 0.2em;
  padding-right: 2em;
}

.hover .copyLink {
  opacity: 1;
}

.root {
  background-color: var(--bs-light-bg-subtle);
  border-radius: unset;
}

.bottomDongle {
  display: block;
  position: absolute;
  margin: 0 auto;
  width: fit-content;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--bs-body-bg);
  border: solid 1px var(--bs-light-border-subtle);
  color: var(--bs-secondary);

  border-radius: var(--bs-border-radius);
  padding: 0em 0.4em;
  cursor: pointer;
}

.dongleIcon {
  padding-right: 0.3em;
}
