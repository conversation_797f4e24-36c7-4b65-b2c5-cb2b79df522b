.navbarContainer {
  display: flex;
  padding-top: 0;
  margin-left: 0.5rem;
  min-width: 250px;
}

.navbarToggle {
  padding: 0rem 0.1rem 0.1rem 0rem;
  display: flex;
}

.navbarBody {
  display: flex;
  flex-direction: column;
  margin-left: 0.2rem;
}

.navbarBodyContainer {
  margin-top: 0.1rem;
  display: grid;
  grid-template-columns: minmax(30px, max-content) minmax(100px, max-content);
}

.navbarTaskTitle {
  font-weight: 600;
  margin-right: 0.3rem;
}

.navbarTaskModel {
  padding-top: 0.4rem;
}

.navbarSecondaryContainer {
  opacity: 0.7;
  margin-top: 0.1rem;
  padding-bottom: 0;
  display: grid;
  grid-template-columns: minmax(0, max-content) max-content;
}

.navbarStatus {
  justify-content: end;
  margin-right: 1em;
  margin-bottom: 0;
}

.navbarWrapper {
  width: 100%;
}
.navbarInnerWrapper {
  display: grid;
  grid-template-columns: 1fr auto;
}
