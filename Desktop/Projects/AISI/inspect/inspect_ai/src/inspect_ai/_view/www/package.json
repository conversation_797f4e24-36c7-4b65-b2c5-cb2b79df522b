{"name": "inspect_ai_log_viewer", "version": "0.0.1", "description": "Log viewer for the inspect_ai package", "license": "MIT", "private": true, "type": "module", "scripts": {"build": "vite build", "watch": "vite build --watch", "dev-watch": "NODE_ENV=development vite build --mode development --watch", "dev-watch-log": "cross-env DEV_LOGGING=true NODE_ENV=development vite build --mode development --watch", "dev": "vite", "prettier:check": "prettier --check src", "prettier:write": "prettier --write src", "test": "NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs --watch", "test:coverage": "NODE_OPTIONS=--experimental-vm-modules jest --config=jest.config.mjs --coverage", "lint": "eslint 'src/**/*.{js,jsx,mjs,ts,tsx}'", "lint:fix": "eslint 'src/**/*.{js,jsx,mjs,ts,tsx}' --fix", "tsc": "tsc --noEmit", "check-all": "yarn tsc && yarn lint:fix && yarn prettier:write && yarn test && yarn build"}, "exports": {".": "./src/App.mjs"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.27.0", "@eslint/js": "^9.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bootstrap": "^5.2.10", "@types/clipboard": "^2.0.10", "@types/codemirror": "^5.60.15", "@types/css-modules": "^1.0.5", "@types/jest": "^29.5.14", "@types/markdown-it": "^14.1.2", "@types/prismjs": "^1.26.5", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "eslint": "9.x", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "json-schema-to-typescript": "^15.0.4", "prettier": "^3.3.3", "ts-jest": "^29.3.2", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "vite": "^5.3.2"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/language": "^6.10.8", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@lezer/highlight": "^1.2.1", "@mui/material": "^7.1.0", "@mui/x-tree-view": "^8.3.0", "@popperjs/core": "^2.11.8", "ansi-output": "^0.0.9", "asciinema-player": "^3.9.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.12.1", "clipboard": "^2.0.11", "clsx": "^2.1.1", "codemirror": "^6.0.1", "fast-json-patch": "^3.1.1", "fflate": "^0.8.2", "filtrex": "^3.1.0", "htm": "^3.1.1", "immer": "^10.1.1", "json": "^11.0.0", "json5": "^2.2.3", "jsondiffpatch": "^0.7.2", "katex": "^0.16.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "murmurhash": "^2.0.1", "postcss-url": "^10.1.3", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-popper": "^2.3.0", "react-router-dom": "^7.6.0", "react-virtuoso": "^4.12.7", "use-resize-observer": "^9.1.0", "zustand": "^5.0.5"}}