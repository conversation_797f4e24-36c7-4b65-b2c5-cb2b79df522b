.findBand {
  position: absolute;
  top: 0;
  right: 0;
  margin-right: 20%;
  z-index: 1060;
  color: var(--inspect-find-foreground);
  background-color: var(--inspect-find-background);
  font-size: 0.9rem;
  display: grid;
  grid-template-columns: auto auto auto auto auto;
  column-gap: 0.2em;
  padding: 0.2rem;
  border-bottom: solid 1px var(--bs-light-border-subtle);
  border-left: solid 1px var(--bs-light-border-subtle);
  border-right: solid 1px var(--bs-light-border-subtle);
  box-shadow: var(--bs-box-shadow);
}

.findBand input {
  height: 2em;
  font-size: 0.9em;
  margin: 0.1rem;
  outline: none;
  border: solid 1px var(--inspect-input-border);
  color: var(--inspect-input-foreground);
  background: var(--inspect-input-background);
}

#inspect-find-no-results {
  font-size: 0.9em;
  opacity: 0;
  margin-top: auto;
  margin-bottom: auto;
  margin-right: 0.5em;
}

.findBand .btn.next,
.findBand .btn.prev {
  padding: 0;
  font-size: var(--inspect-fond-size-larger);
}

.findBand .btn.close {
  padding: 0;
  font-size: var(--inspect-font-size-title-secondary);
  margin-top: -0.1rem;
  margin-bottom: -0.1rem;
}
