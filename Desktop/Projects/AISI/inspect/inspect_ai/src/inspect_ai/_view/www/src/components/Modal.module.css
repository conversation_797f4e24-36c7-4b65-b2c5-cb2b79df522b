.modal {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex: 1 1 auto;
  justify-content: center;
  max-width: 90vw;
  margin-top: 3rem;
  border-radius: var(--bs-border-radius);
  position: relative;
  z-index: 1050;
}

.header {
  height: 2em;
}

.modalTitle {
  font-weight: 600;
}

.btnClose {
  height: 0.8em;
  width: 0.8em;
}

/* Backdrop styling for the glass effect */
.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--inspect-glass-color);
  opacity: var(--inspect-glass-opacity);

  z-index: 1040;
}
