.container {
  display: flex;
  padding-top: 0;
  margin-left: 0.5rem;
  min-width: 250px;
}

.wrapper {
  display: grid;
  grid-template-columns: minmax(auto, 1fr) 1fr;
  width: 100%;
}

.toggle {
  padding: 0rem 0.1rem 0.1rem 0rem;
  display: flex;
}

.body {
  display: flex;
  flex-direction: column;
  margin-left: 0.2rem;
}

.bodyContainer {
  margin-top: 0.1rem;
  display: grid;
  grid-template-columns: minmax(30px, max-content) minmax(100px, max-content);
}

.taskTitle {
  font-weight: 600;
  margin-right: 0.3rem;
}

.taskModel {
  padding-top: 0.4rem;
}

.taskStatus {
  display: flex;
  justify-content: end;
  margin-right: 1em;
  margin-bottom: 0;
}

.secondaryContainer {
  opacity: 0.7;
  margin-top: -0.1rem;
  padding-bottom: 0;
  display: grid;
  grid-template-columns: minmax(0, max-content) max-content;
}
