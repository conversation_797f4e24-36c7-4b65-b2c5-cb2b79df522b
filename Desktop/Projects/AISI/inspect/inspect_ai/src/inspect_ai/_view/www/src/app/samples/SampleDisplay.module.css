.tabPanel {
  padding-bottom: 1em;
}

.tabControls {
  position: sticky;
  z-index: 1001;
  top: 0;
  background-color: var(--bs-body-bg);
}

.fullWidth {
  width: 100%;
}

.metadataPanel {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  gap: 1em;
  padding-left: 0;
  margin-top: 0.5em;
}

.padded {
  padding-left: 0.8em;
  margin-top: 0.4em;
}

.error {
  padding-top: 0.5em;
}

.ansi {
  margin: 1em 0;
}

.noTop {
  margin-top: 0;
}

.timePanel {
  display: grid;
  grid-template-columns: max-content max-content;
  grid-template-rows: auto;
  column-gap: 0.5em;
  min-width: 200px;
}

.chat {
  padding: 1em;
}

.padded {
  padding: 1em;
}

.transcriptContainer {
  padding-bottom: 1em;
}
