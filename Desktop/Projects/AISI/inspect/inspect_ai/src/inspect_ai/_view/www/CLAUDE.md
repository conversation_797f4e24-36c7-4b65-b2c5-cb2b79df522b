# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Lint/Test Commands

- Run type checker, linter, and tests: `yarn check-all`
- Run type checker: `yarn tsc`
- Run linter: `yarn lint`
- Run tests: `yarn test`

## Other Information

- The code in this project is typescript, learn more about the configuration by inspecting package.json.
- Respect existing code patterns when modifying files. Run linting before committing changes.
