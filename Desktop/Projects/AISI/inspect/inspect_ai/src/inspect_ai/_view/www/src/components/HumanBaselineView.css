.asciinema-player {
  max-height: 100vh;
  max-width: 100vw;
}

.asciinema-player-status {
  margin-right: 0.5em;
}

.asciinema-wrapper {
  display: flex;
  justify-content: center;
}

.asciinema-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  width: 100%;
}

.asciinema-header-left {
  justify-self: start;
  font-size: var(--inspect-font-size-small);
}

.asciinema-header-center {
  justify-self: center;
  font-size: var(--inspect-font-size-small);
}

.asciinema-header-right {
  justify-self: start;
  font-size: var(--inspect-font-size-small);
}

.asciinema-body {
  border-top: 1px solid var(--bs-light-border-subtle);
  grid-column: span 3;
  width: 100%;
}
