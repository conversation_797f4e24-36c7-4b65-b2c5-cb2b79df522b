.grid {
  display: grid;
  padding-top: 1em;
  padding-bottom: 1em;
  border-bottom: solid var(--bs-border-color) 1px;
  cursor: pointer;
  overflow-y: hidden;
  grid-gap: 10px;
  padding-left: 1rem;
  padding-right: 1rem;
}

.selected {
  box-shadow: inset 0 0 0px 2px var(--bs-focus-ring-color);
}

.sampleRowLink {
  text-decoration: none;
  color: inherit;
  display: block;
}

.disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.cell {
  padding-left: 0;
  padding-right: 0;
}

.wrapAnywhere {
  word-wrap: anywhere;
}

.noLeft {
  padding-left: 0;
}

.score {
  display: flex;
  justify-self: center;
}

.centered {
  display: flex;
  justify-content: center;
}

.spinner {
  height: 1.4em;
  width: 1.4em;
  color: var(--bs-focus-ring-color);
}
