.container {
  margin: 0.5em 0 0 0;
  width: 100%;
}

.all {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 1em;
}

.tableSelection {
  width: fit-content;
  align-self: start;
  justify-self: start;
}

.tools {
  grid-column: -1/1;
}

.codePre {
  background: var(--bs-light);
  width: 100%;
  padding: 0.5em;
  border-radius: var(--bs-border-radius);
}

.code {
  white-space: pre-wrap !important;
  word-wrap: anywhere !important;
}

.progress {
  margin-left: 0.5em;
}

.toolConfig {
  display: grid;
  grid-template-columns: max-content auto;
  column-gap: 1em;
  row-gap: 0.5em;
  padding-top: 0.5em;
}

.toolChoice {
  border-top: solid var(--bs-light-border-subtle) 1px;
  display: grid;
  grid-template-columns: max-content auto;
  column-gap: 1em;
  margin-top: 0.5em;
  padding-top: 0.5em;
}
