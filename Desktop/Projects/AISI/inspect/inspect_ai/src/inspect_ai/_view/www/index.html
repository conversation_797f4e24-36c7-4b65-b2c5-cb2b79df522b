<!doctype html>
<html lang="en" data-bs-theme="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Inspect View</title>
    <link rel="icon" href="./favicon.svg" />

    <script>
      // Forward the theme (dark or light) onto the root html element
      // this parameter will tend to appear when the view is hosted within
      // an iframe (e.g. in vscode)
      const urlParams = new URLSearchParams(window.location.search);
      const theme = urlParams.get("inspectLogviewThemeCategory");
      if (theme) {
        document.documentElement.setAttribute("data-text-highlight", theme);
        document.documentElement.setAttribute("data-bs-theme", theme);
      }
    </script>
  </head>

  <body style="min-width: 450px">
    <div id="app"></div>
    <script type="module" src="./src/index.tsx"></script>
  </body>
</html>
