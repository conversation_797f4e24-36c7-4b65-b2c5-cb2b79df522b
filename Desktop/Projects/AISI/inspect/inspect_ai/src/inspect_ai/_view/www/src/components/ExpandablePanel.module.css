.expandablePanel {
  position: relative;
}

.expandableBordered {
  border: solid var(--bs-light-border-subtle) 1px;
  padding: 0.5em;
}

.expandableTogglable {
  margin-bottom: 1em;
}

.expandableContents {
  font-size: var(--inspect-font-size-base);
}

.expandableCollapsed {
  overflow: hidden;
}

.moreToggle {
  display: flex;
  margin-top: 0;
  position: absolute;
  bottom: 0.25em;
  right: 0.25em;
  height: 20px;
  background-color: var(--bs-body-bg);
  border-radius: 5px;
  border: solid var(--bs-light-border-subtle) 1px;
  color: var(--bs-link-color);
}

.moreToggle.bordered {
  border-top: solid var(--bs-light-border-subtle) 1px;
}

.moreToggleButton {
  font-size: var(--inspect-font-size-smaller);
  border: none;
  padding: 0rem 0.5rem;
}

.separator {
  height: 1px;
  background-color: var(--bs-light-border-subtle);
  margin-top: -1px;
}
