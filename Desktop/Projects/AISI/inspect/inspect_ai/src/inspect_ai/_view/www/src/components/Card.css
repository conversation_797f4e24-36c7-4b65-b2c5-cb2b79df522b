.card-header-container {
  display: grid;
  grid-template-columns: max-content auto;
  column-gap: 0em;
  padding: 0.5em 0.5em 0.5em 0.5em;
  font-size: var(--inspect-font-size-small);
  font-weight: 600;
  border-bottom: solid 1px var(--bs-light-border-subtle);
}

.card-header-icon {
  padding-right: 0.2rem;
}

.card-body {
  background-color: var(--bs-body-bg);

  padding: 0.5em;
}

.card {
  background-color: var(--bs-light-bg-subtle);
  border: solid 1px var(--bs-light-border-subtle);
  border-radius: var(--bs-border-radius);
}

.card-collaping-header {
  border-bottom: none;
}

.card-collapsing-header-container {
  justify-content: space-between;
  align-items: center;
}

.card-collapsing-header-icon {
  flex: 0 0 content;
  padding-right: 0.5rem;
}

.card-collapsing-header-contents {
  color: var(--body-color);
  opacity: 0.8;
  flex: 1 1 auto;
  font-size: var(--inspect-font-size-smaller);
  padding-right: 0;
  padding-left: 0;
  transition: opacity 0.2s ease-out;
  display: flex;
  justify-content: space-between;
}

.card-collapsing-header-toggle {
  flex: 0 1 1em;
  text-align: right;
  padding: 0 0.5em 0.1em 0.5em;
  font-size: var(--inspect-font-size-smaller);
}

.card-body.card-no-padding {
  padding: 0;
}
