import time as python_time

from pydantic import BaseModel, Field

from inspect_ai.scorer._metric import Score
from inspect_ai.util._store_model import StoreModel


class IntermediateScoring(BaseModel):
    time: float
    scores: list[Score]


class HumanAgentState(StoreModel):
    instructions: str
    """Task instructions."""

    @property
    def running(self) -> bool:
        """Is the task currently running?"""
        return self.running_state

    @running.setter
    def running(self, running: bool) -> None:
        """Set current running state."""
        # if we are flipping to running mode then update started running
        if not self.running_state and running:
            self.started_running = python_time.time()

        # if we are exiting running mode then update accumulated time
        if self.running_state and not running:
            self.accumulated_time = self.time

        # update running
        self.running_state = running

    @property
    def time(self) -> float:
        """Total time spend on task."""
        running_time = python_time.time() - self.started_running if self.running else 0
        return self.accumulated_time + running_time

    scorings: list[IntermediateScoring] = Field(default_factory=list)
    """Intermediate scorings yielded by `task score`"""

    answer: str | None = Field(default=None)
    """Final answer provided in `task submit`"""

    logs: dict[str, str] = Field(default_factory=dict)
    """Session logs generated by `script` """

    # internal state variables used by running and time properties
    running_state: bool = Field(default=False)
    started_running: float = Field(default_factory=python_time.time)
    accumulated_time: float = Field(default=0.0)
