from ..._agent import <PERSON><PERSON>tate
from .clock import <PERSON><PERSON>ommand, StopCommand
from .command import <PERSON><PERSON>gent<PERSON>ommand
from .instructions import InstructionsCommand
from .note import NoteCommand
from .score import ScoreCommand
from .status import StatusCommand
from .submit import QuitCommand, SubmitCommand, <PERSON><PERSON><PERSON><PERSON><PERSON>mand


def human_agent_commands(
    state: AgentState,
    answer: bool | str,
    intermediate_scoring: bool,
    record_session: bool,
) -> list[HumanAgentCommand]:
    # base submit, validate, and quit
    commands = [
        SubmitCommand(record_session),
        Validate<PERSON>ommand(answer),
        QuitCommand(record_session),
    ]

    # optional intermediate scoring
    if intermediate_scoring:
        commands.append(ScoreCommand(state))

    # remaining commands
    commands.extend(
        [
            NoteCommand(),
            StatusCommand(),
            StartCommand(),
            StopCommand(),
        ]
    )

    # with instructions (letting it see the other commands)
    return commands + [InstructionsCommand(commands)]
