```{=html}

<% 
function groupIcon(group) {
  switch(group) {
    case "Coding":
      return "code";
    case "Assistants":
      return "info-circle";
    case "Cybersecurity":
      return "laptop";
    case "Safeguards":
      return "shield-lock";
    case "Mathematics":
      return "calculator";
    case "Reasoning":
      return "boxes";
    case "Personality":
      return "people";
    case "Scheming":
      return "person-gear";
    default:
      return "book"
  }
}
%>

<ul class="list no-bullets">

<% let group="" %> 

<% for (let i = 0; i < items.length; i++){ %>
  <% item=items[i] %>
  <% if (item["group"] !== group) { %>
  <% group=item["group"] %>  
<li class="group"><h2 href="#<%= item['group'].toLowerCase() %>"><%= item["group"] %></h2></li>
  <% } %>  
  <% item=items[i] %>
<li class="example" <%= metadataAttrs(item) %>>
    <div class="example-card">
      <div class="example-icon fs-5">
        <i class="bi bi-<%= groupIcon(group) %>"></i>
      </div>
      <div class="example-info">
        <div class="listing-title">
          <a href="https://github.com/UKGovernmentBEIS/inspect_evals/tree/main/<%- item.path %>">
            <%= item["title"] %>
          </a>
        </div>
        <div class="listing-description text-secondary"><%= item["description"] %></div>
      </div>
    </div>
  </a>
</li>
<% } %>  
</ul>
```

  
