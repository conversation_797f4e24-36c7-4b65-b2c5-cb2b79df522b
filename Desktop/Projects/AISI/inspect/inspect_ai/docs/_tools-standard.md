Inspect has several standard tools built-in, including:

-   [Web Search](tools-standard.qmd#sec-web-search), which uses a search provider (either built in to the model or external) to execute and summarize web searches.

-   [Bash and Python](tools-standard.qmd#sec-bash-and-python) for executing arbitrary shell and Python code.

-   [Bash Session](tools-standard.qmd#sec-bash-session) for creating a stateful bash shell that retains its state across calls from the model.

-   [Text Editor](tools-standard.qmd#sec-text-editor) which enables viewing, creating and editing text files.

-   [Web Browser](tools-standard.qmd#sec-web-browser), which provides the model with a headless Chromium web browser that supports navigation, history, and mouse/keyboard interactions.

-   [Computer](tools-standard.qmd#sec-computer), which provides the model with a desktop computer (viewed through screenshots) that supports mouse and keyboard interaction.

-   [Think](tools-standard.qmd#sec-think), which provides models the ability to include an additional thinking step as part of getting to its final answer.
