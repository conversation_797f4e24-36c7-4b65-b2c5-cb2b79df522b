---
title: Typing
---

## Overview

The Inspect codebase is written using strict [MyPy](https://mypy-lang.org/) type-checking---if you enable the same for your project along with installing the [MyPy VS Code Extension](https://marketplace.visualstudio.com/items?itemName=ms-python.mypy-type-checker) you'll benefit from all of these type definitions.

The sample store and sample metadata interfaces are weakly typed to accommodate arbitrary user data structures. Below, we describe how to implement a [typed store](#typed-store) and [typed metadata](#typed-metadata) using Pydantic models.

## Typed Store

{{< include _store_typing.md >}}

## Typed Metadata

{{< include _metadata_typing.md >}}

## Log Samples

The `store_as()` and `metadata_as()` typed accessors are also available when reading samples from the eval log. Continuing from the examples above, you access typed interfaces as follows from an `EvalLog`:

```python
# typed store
activity = log.samples[0].store_as(Activity)

# typed metadata
metadata = log.samples[0].metadata_as(PopularityMetadata)
```
