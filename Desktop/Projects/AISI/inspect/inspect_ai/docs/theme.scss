/*-- scss:rules --*/

#TOC {
    margin-top: 18px;
}

img.navbar-logo {
    padding-right: 15px;
}

.level1 h1 {
    margin-top: 0;
}

.sidebar>.sidebar-menu-container>.list-unstyled>.sidebar-item {
    margin-bottom: 1em;
}

.sidebar-header {
    margin-top: 0 !important;
}

.sidebar-menu-container {
    padding-top: 10px !important;
}

#quarto-sidebar div:nth-of-type(3) {
    padding-top: 5px !important;
}

.sidebar-menu-container>ul>li:first-of-type {
    margin-bottom: 0.7em !important;
}

.sidebar-item-section {
    margin-bottom: 0.6em !important;
}

.sidebar-header-item>p {
    margin-bottom: 0;
}

.sidebar-item-section  .sidebar-item-section {
    margin-bottom: 0 !important;    
}


.sidebar-tools-main .quarto-navigation-tool[title="Source Code"] {
    padding-top: 2.5px;
}

.code-tabset {
    margin-bottom: 1em;
}

.code-tabset .tab-content {
    padding: 0;
    margin-bottom: 0;
}

.code-tabset div.sourceCode {
    border: none;
    margin: 0;
}

.code-tabset .nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    border-bottom-color: $border-color;
}

.quarto-layout-panel .sourceCode {
    margin-top: 0;
    margin-bottom: 0.5em;
}

.splash ul {
    padding-inline-start: 1rem;
}

@media(max-width: 991.98px) {
    .sidebar-header-item .img-fluid {
        max-width: 195px;
    }
}

.blockquote {
    color: #505a62;
}

.source-link {
    position: relative;
}

.source-link > a {
    color: $btn-code-copy-color;
    position: absolute;
    right: 5px;
    top: 0;
    font-size: 0.8em;
    z-index: 1001;
}

.source-link a:hover {
    color: $btn-code-copy-color-active;
}

.element-type {
    font-size: 0.8em;
    font-weight: 400;
}

.element-type-name > a {
    border-bottom: 1px dotted currentcolor !important;
    color: currentColor;
    text-decoration: none;
}

.element-type-name > a:hover {
    text-decoration: underline;
    cursor: pointer;
}

.ref-interlink {
    color: $code-color;
    background-color: $code-bg;
    font-family: $font-family-monospace;
    font-size: 0.875em;
}

.doc-declaration .code-copy-button {
    display: none;
}

.class-methods > dl > dt {
    font-size: 1.3em;
}

.ref-definition {
    font-weight: normal;
}

.welcome-bullets li {
    margin-bottom: 0.5em;
}