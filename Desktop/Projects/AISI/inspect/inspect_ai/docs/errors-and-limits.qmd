---
title: "Errors and Limits"
---

## Overview

When developing more complex evaluations, its not uncommon to encounter error conditions during development—these might occur due to a bug in a solver or scorer, an unreliable or overloaded API, or a failure to communicate with a sandbox environment. It's also possible to end up evals that don't terminate properly because models continue running in a tool calling loop even though they are "stuck" and very unlikely to make additional progress.

This article covers various techniques for dealing with unexpected errors and setting limits on evaluation tasks and samples. Topics covered include:

1.  Retrying failed evaluations (while preserving the samples completed during the initial failed run).
2.  Establishing a threshold (count or percentage) of samples to tolerate errors for before failing an evaluation.
3.  Setting time limits for samples (either running time or more narrowly execution time).
4.  Setting a maximum number of messages or tokens in a sample before forcing the model to give up.

{{< include _errors_and_retries.md >}}

## Failure Threshold

In some cases you might wish to tolerate some number of errors without failing the evaluation. This might be during development when errors are more commonplace, or could be to deal with a particularly unreliable API used in the evaluation. Add the `fail_on_error` option to your `Task` definition to establish this threshold. For example, here we indicate that we'll tolerate errors in up to 10% of the total sample count before failing:

``` python
@task
def intercode_ctf():
    return Task(
        dataset=read_dataset(),
        solver=[
            system_message("system.txt"),
            use_tools([bash(timeout=120)]),
            generate(),
        ],
        fail_on_error=0.1,
        scorer=includes(),
        sandbox="docker",
    )
```

Failed samples are *not scored* and a warning indicating that some samples failed is both printed in the terminal and shown in Inspect View when this occurs.

You can specify `fail_on_error` as a boolean (turning the behaviour on and off entirely), as a number between 0 and 1 (indicating a proportion of failures to tolerate), or a number greater than 1 to (indicating a count of failures to tolerate):

| Value                 | Behaviour                                           |
|---------------------------|---------------------------------------------|
| `fail_on_error=True`  | Fail eval immediately on sample errors (default).   |
| `fail_on_error=False` | Never fail eval on sample errors.                   |
| `fail_on_error=0.1`   | Fail if more than 10% of total samples have errors. |
| `fail_on_error=5`     | Fail eval if more than 5 samples have errors.       |

: {tbl-colwidths=\[40,60\]}

While `fail_on_error` is typically specified at the `Task` level, you can also override the task setting when calling `eval()` or `inspect eval` from the CLI. For example:

``` python
eval("intercode_ctf.py", fail_on_error=False)
```

You might choose to do this if you want to tolerate a certain proportion of errors during development but want to ensure there are never errors when running in production.

## Sample Retries

The `retry_on_error` option enables retrying samples with errors some number of times before they are considered failed (and subject to `fail_on_error` processing as described above). For example:

``` bash
inspect eval ctf.py --retry-on-error    # retry 1 time
inspect eval ctf.py --retry-on-error=3  # retry up to 3 times
```

Or from Python:

``` python
eval("ctf.py", retry_on_error=1)
```

If a sample is retried, the original error(s) that induced the retries will be recorded in its `error_retries` field.

::: {.callout-warning appearance="simple"}
#### Retries and Distribution Shift

While sample retries enable improved recovery from transient infrastructure errors, they also carry with them some risk of distribution shift. For example, imagine that the error being retried is a bug in one of your agents that is triggered by only certain classes of input. These classes of input could then potentially have a higher chance of success because they will be "re-rolled" more frequently.

Consequently, when enabling `retry_on_error` you should do some post-hoc analysis to ensure that retried samples don't have significantly different results than samples which are not retried.
:::

## Sample Limits {#sample-limits}

In open-ended model conversations (for example, an agent evaluation with tool usage) it's possible that a model will get "stuck" attempting to perform a task with no realistic prospect of completing it. Further, sometimes models will call commands in a sandbox that take an extremely long time (or worst case, hang indefinitely).

For this type of evaluation it's normally a good idea to set sample level limits on some combination of total time, total messages, and/or tokens used. Sample limits don't result in errors, but rather an early exit from execution (samples that encounter limits are still scored, albeit nearly always as "incorrect").

### Time Limit

Here we set a `time_limit` of 15 minutes (15 x 60 seconds) for each sample within a task:

``` python
@task
def intercode_ctf():
    return Task(
        dataset=read_dataset(),
        solver=[
            system_message("system.txt"),
            use_tools([bash(timeout=3 * 60)]),
            generate(),
        ],
        time_limit=15 * 60,
        scorer=includes(),
        sandbox="docker",
    )
```

Note that we also set a timeout of 3 minutes for the `bash()` command. This isn't required but is often a good idea so that a single wayward bash command doesn't consume the entire `time_limit`.

We can also specify a time limit at the CLI or when calling `eval()`:

``` bash
inspect eval ctf.py --time-limit 900
```

Appropriate timeouts will vary depending on the nature of your task so please view the above as examples only rather than recommend values.

### Working Limit

{{< include _working_limits.md >}}

Here we set an `working_limit` of 10 minutes (10 x 60 seconds) for each sample within a task:

``` python
@task
def intercode_ctf():
    return Task(
        dataset=read_dataset(),
        solver=[
            system_message("system.txt"),
            use_tools([bash(timeout=3 * 60)]),
            generate(),
        ],
        working_limit=10 * 60,
        scorer=includes(),
        sandbox="docker",
    )
```


### Message Limit

{{< include _message_limits.md >}}

Here we set a `message_limit` of 30 for each sample within a task:

``` python
@task
def intercode_ctf():
    return Task(
        dataset=read_dataset(),
        solver=[
            system_message("system.txt"),
            use_tools([bash(timeout=120)]),
            generate(),
        ],
        message_limit=30,
        scorer=includes(),
        sandbox="docker",
    )
```

This sets a limit of 30 total messages in a conversation before the model is forced to give up. At that point, whatever `output` happens to be in the `TaskState` will be scored (presumably leading to a score of incorrect).

### Token Limit

{{< include _token_limits.md >}}

Here we set a `token_limit` of 500K for each sample within a task:

``` python
@task
def intercode_ctf():
    return Task(
        dataset=read_dataset(),
        solver=[
            system_message("system.txt"),
            use_tools([bash(timeout=120)]),
            generate(),
        ],
        token_limit=(1024*500),
        scorer=includes(),
        sandbox="docker",
    )
```

::: callout-important
It's important to note that the `token_limit` is for all tokens used within the execution of a sample. If you want to limit the number of tokens that can be yielded from a single call to the model you should use the `max_tokens` generation option.
:::

### Custom Limit

When limits are exceeded, a `LimitExceededError` is raised and caught by the main Inspect sample execution logic. If you want to create custom limit types, you can enforce them by raising a `LimitExceededError` as follows:

``` python
from inspect_ai.util import LimitExceededError

raise LimitExceededError(
    "custom", 
    value=value,
    limit=limit,
    message=f"A custom limit was exceeded: {value}"
)
```

### Query Usage

We can determine how much of a sample limit has been used, what the limit is, and how much of the resource is remaining:

``` python
sample_time_limit = sample_limits().time
print(f"{sample_time_limit.remaining:.0f} seconds remaining")
```

Note that `sample_limits()` only retrieves the sample-level limits, not [scoped limits](#scoped-limits) or [agent limits](#agent-limits).


## Scoped Limits {#scoped-limits}

You can also apply limits at arbitrary scopes, independent of the sample or agent-scoped limits. For instance, applied to a specific block of code. For example:

``` python
with token_limit(1024*500):
    ...
```

A `LimitExceededError` will be raised if the limit is exceeded. The `source` field on `LimitExceededError` will be set to the `Limit` instance that was exceeded.

When catching `LimitExceededError`, ensure that your `try` block encompasses the usage of the limit context manager as some `LimitExceededError` exceptions are raised at the scope of closing the context manager:

``` python
try:
    with token_limit(1024*500):
        ...
except LimitExceededError:
    ...
```

The `apply_limits()` function accepts a list of `Limit` instances. If any of the limits passed in are exceeded, the `limit_error` property on the `LimitScope` yielded when opening the context manager will be set to the exception. By default, all `LimitExceededError` exceptions are propagated. However, if `catch_errors` is true, errors which are as a direct result of exceeding one of the limits passed to it will be caught. It will always allow `LimitExceededError` exceptions triggered by other limits (e.g. Sample scoped limits) to propagate up the call stack.

``` python
with apply_limits(
    [token_limit(1000), message_limit(10)], catch_errors=True
) as limit_scope:
    ...
if limit_scope.limit_error:
    print(f"One of our limits was hit: {limit_scope.limit_error}")

```

### Checking Usage

You can query how much of a limited resource has been used so far via the `usage` property of a scoped limit. For example:

``` python
with token_limit(10_000) as limit:
    await generate()
    print(f"Used {limit.usage:,} of 10,000 tokens")
```

If you're passing the limit instance to `apply_limits()` or an agent and want to query the usage, you should keep a reference to it:

``` python
limit = token_limit(10_000)
with apply_limits([limit]):
    await generate()
    print(f"Used {limit.usage:,} of 10,000 tokens")
```

### Time Limit

To limit the wall clock time to 15 minutes within a block of code:

``` python
with time_limit(15 * 60):
    ...
```

Internally, this uses [`anyio`'s cancellation scopes](https://anyio.readthedocs.io/en/stable/cancellation.html). The block will be cancelled at the first yield point (e.g. `await` statement). 

### Working Limit

{{< include _working_limits.md >}}

To limit the working time to 10 minutes:

``` python
with working_limit(10 * 60):
    ...
```

Unlike time limits, this is not driven by `anyio`. It is checked periodically such as from `generate()` and after each `Solver` runs.


### Message Limit

{{< include _message_limits.md >}}

Scoped message limits behave differently to scoped token limits in that only the innermost active `message_limit()` is checked.

To limit the conversation length within a block of code:

``` python
@agent
def myagent() -> Agent:
    async def execute(state: AgentState):

        with message_limit(50):
            # A LimitExceededError will be raised when the limit is exceeded
            ...
            with message_limit(None):
                # The limit of 50 is temporarily removed in this block of code
                ...
```

::: callout-important
It's important to note that `message_limit()` limits the total number of messages in the conversation, not just "new" messages appended by an agent.
:::

### Token Limit

{{< include _token_limits.md >}}

To limit the total number of tokens which can be used in a block of code:

``` python
@agent
def myagent(tokens: int = (1024*500)) -> Agent:
    async def execute(state: AgentState):

        with token_limit(tokens):
            # a LimitExceededError will be raised if the limit is exceeded
            ...
```

The limits can be stacked. Tokens used while a context manager is open count towards all open token limits.

``` python
@agent
def myagent() -> Solver:
    async def execute(state: AgentState):

        with token_limit(1024*500):
            ...
            with token_limit(1024*200):
                # Tokens used here count towards both active limits
                ...
```

::: callout-important
It's important to note that `token_limit()` is for all tokens used *while the context manager is open*. If you want to limit the number of tokens that can be yielded from a single call to the model you should use the `max_tokens` generation option.
:::

## Agent Limits {#agent-limits}

{{< include _agent_limits.md >}}
