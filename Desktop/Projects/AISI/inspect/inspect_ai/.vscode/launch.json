{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Module",
      "type": "debugpy",
      "request": "launch",
      "module": "inspect_ai",
      "args": "${input:argsPrompt}",
      "cwd": "${workspaceFolder}/src"
    },
    {
      "name": "Python Debugger: Pytest",
      "type": "debugpy",
      "request": "launch",
      "module": "pytest",
      "args": ["-k", "test_mcp_server_sandbox"],
      // "args": ["-k", "test_mcp_server_sandbox", "--runapi"],
      "cwd": "${workspaceFolder}"
    }
  ],
  "inputs": [
    {
      "id": "argsPrompt",
      "type": "promptString",
      "description": "Enter arguments for the module",
      "default": ""
    }
  ]
}
