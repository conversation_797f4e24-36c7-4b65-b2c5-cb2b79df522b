{"name": "inspect-ai <PERSON>container", "build": {"dockerfile": "Dockerfile", "context": ".."}, "workspaceMount": "source=${localWorkspaceFolder},target=/home/<USER>/app,type=bind,consistency=cached", "workspaceFolder": "/home/<USER>/app", "mounts": [{"source": "${localEnv:HOME}${localEnv:USERPROFILE}/.aws", "target": "/home/<USER>/.aws", "type": "bind"}, {"source": "inspect-ai-home", "target": "/home/<USER>", "type": "volume"}, {"source": "inspect-ai-docker-data", "target": "/var/lib/docker", "type": "volume"}], "runArgs": ["--name=inspect-ai-dev", "--hostname=inspect-ai", "--privileged"], "overrideCommand": false, "remoteUser": "inspect"}